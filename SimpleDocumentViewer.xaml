<UserControl x:Class="IntechApplication.SimpleDocumentViewer"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:IntechApplication"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,0,0,1" Padding="20">
            <StackPanel>
                <TextBlock Name="DocumentTitle" Text="Document Title" FontSize="18" FontWeight="Bold" Margin="0,0,0,10"/>
                <StackPanel Orientation="Horizontal">
                    <Button Name="OpenButton" Content="📄 Open with Default App" Margin="0,0,10,0" Padding="10,5" Click="OpenButton_Click"/>
                    <Button Name="CopyPathButton" Content="📋 Copy Path" Margin="0,0,10,0" Padding="10,5" Click="CopyPathButton_Click"/>
                    <TextBlock Name="FileInfoText" Text="File info" VerticalAlignment="Center" Foreground="#666" Margin="10,0,0,0"/>
                </StackPanel>
            </StackPanel>
        </Border>
        
        <!-- Content Area -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                <!-- Document Preview Message -->
                <Border Background="#FFF3CD" BorderBrush="#FFEAA7" BorderThickness="1" CornerRadius="5" Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="📄 Document Preview" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                        <TextBlock Text="This is a Word/Excel document. To view the full content, please open it with Microsoft Office or a compatible application." 
                                   TextWrapping="Wrap" Margin="0,0,0,10"/>
                        <TextBlock Name="ConversionStatusText" Text="PDF conversion requires Microsoft Office to be installed." 
                                   TextWrapping="Wrap" Foreground="#856404"/>
                    </StackPanel>
                </Border>
                
                <!-- Document Information -->
                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="5" Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="📋 Document Information" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="File Name:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Name="FileNameText" Text="" Margin="0,0,0,5"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="File Size:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Name="FileSizeText" Text="" Margin="0,0,0,5"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Last Modified:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Name="LastModifiedText" Text="" Margin="0,0,0,5"/>
                            
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="File Path:" FontWeight="Bold" Margin="0,0,10,5"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Name="FilePathText" Text="" TextWrapping="Wrap" Margin="0,0,0,5"/>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Quick Actions -->
                <Border Background="#E7F3FF" BorderBrush="#B3D9FF" BorderThickness="1" CornerRadius="5" Padding="15">
                    <StackPanel>
                        <TextBlock Text="⚡ Quick Actions" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        <StackPanel Orientation="Horizontal">
                            <Button Name="OpenFolderButton" Content="📁 Open Folder" Margin="0,0,10,0" Padding="10,5" Click="OpenFolderButton_Click"/>
                            <Button Name="RefreshButton" Content="🔄 Refresh" Margin="0,0,10,0" Padding="10,5" Click="RefreshButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>

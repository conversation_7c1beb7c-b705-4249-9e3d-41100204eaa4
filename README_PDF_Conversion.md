# PDF Conversion Setup Guide

## Overview
This application supports converting Office documents (Word, Excel, PowerPoint) to PDF for in-app preview. The conversion uses multiple fallback methods to ensure compatibility.

## Conversion Methods (in order of priority)

### 1. LibreOffice (Recommended - Free & Cross-platform)
**Advantages:**
- Free and open-source
- Supports Word, Excel, PowerPoint
- Cross-platform (Windows, Mac, Linux)
- Command-line interface for automation
- No licensing issues

**Installation:**
1. Download from: https://www.libreoffice.org/download/download/
2. Install with default settings
3. The app will automatically detect LibreOffice installation

**Supported paths checked:**
- `C:\Program Files\LibreOffice\program\soffice.exe`
- `C:\Program Files (x86)\LibreOffice\program\soffice.exe`
- `C:\Program Files\LibreOffice 7.6\program\soffice.exe`
- `C:\Program Files (x86)\LibreOffice 7.6\program\soffice.exe`
- `soffice` (if in PATH)

### 2. Microsoft Office (Fallback)
**Advantages:**
- Native support for Office formats
- High fidelity conversion

**Requirements:**
- Microsoft Office installed (Word, Excel, PowerPoint)
- COM Interop enabled
- May require Office license

### 3. Document Viewer (Final Fallback)
**When conversion fails:**
- Shows document information
- Provides buttons to open with default application
- Copy file path to clipboard
- Open containing folder

## Testing Conversion

### Test LibreOffice Installation:
```bash
# Open Command Prompt and test:
"C:\Program Files\LibreOffice\program\soffice.exe" --version

# Or if in PATH:
soffice --version
```

### Test Conversion Manually:
```bash
# Convert Word to PDF:
soffice --headless --convert-to pdf --outdir "C:\temp" "C:\path\to\document.docx"

# Convert Excel to PDF:
soffice --headless --convert-to pdf --outdir "C:\temp" "C:\path\to\spreadsheet.xlsx"
```

## Troubleshooting

### LibreOffice Not Detected
1. Verify installation path matches expected locations
2. Try reinstalling LibreOffice
3. Add LibreOffice to system PATH
4. Check Windows permissions

### Office Interop Errors
1. Ensure Microsoft Office is properly installed
2. Run application as Administrator
3. Check Office version compatibility
4. Repair Office installation

### PDF Viewer Issues
1. Ensure PdfiumViewer package is installed
2. Check file permissions
3. Verify PDF file is not corrupted

## PDF Viewer Technology

### PdfiumViewer (Current)
**Advantages:**
- Custom PDF rendering controls
- Zoom in/out, page navigation
- Fit to width functionality
- Direct PDF rendering

**Requirements:**
- `PdfiumViewer` package (installed)
- `PdfiumViewer.Native.x86_64.v8-xfa` package (installed)
- `System.Drawing.Common` package (installed)

**Note:** If you encounter DLL loading issues:
1. Ensure all packages are properly installed
2. Try running as Administrator
3. Check Windows dependencies

## Application Behavior

### Successful Conversion:
1. Shows "Converting [document] to PDF..." message
2. Displays PDF in PdfiumViewer integrated viewer
3. Provides zoom in/out, fit width, page navigation
4. Custom PDF rendering with toolbar controls

### Failed Conversion:
1. Shows document information panel
2. Provides "Open with Default App" button
3. Shows file details (size, modified date, path)
4. Offers quick actions (copy path, open folder)

## Performance Notes

- PDF files are cached (only converted if source is newer)
- LibreOffice conversion is typically faster than Office Interop
- First conversion may take longer due to LibreOffice startup
- Large documents may take several seconds to convert

## Supported File Types

**Full Conversion Support:**
- `.docx`, `.doc` (Word documents)
- `.xlsx`, `.xls` (Excel spreadsheets)
- `.pptx`, `.ppt` (PowerPoint presentations - LibreOffice only)

**Direct Display:**
- `.pdf` (no conversion needed)

**Document Viewer Only:**
- Any other file types (shows info + open button)

using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Office.Interop.Word;
using System.Runtime.InteropServices;
using System.Diagnostics;

namespace IntechApplication
{
    public static class OfficeToPdfConverter
    {
        public static async Task<string?> ConvertToPdfAsync(string inputFilePath)
        {
            if (!File.Exists(inputFilePath))
            {
                throw new FileNotFoundException($"Input file not found: {inputFilePath}");
            }

            var extension = Path.GetExtension(inputFilePath).ToLower();
            var outputPath = Path.ChangeExtension(inputFilePath, ".pdf");

            // Check if PDF already exists and is newer than source
            if (File.Exists(outputPath))
            {
                var sourceTime = File.GetLastWriteTime(inputFilePath);
                var pdfTime = File.GetLastWriteTime(outputPath);
                if (pdfTime >= sourceTime)
                {
                    return outputPath; // PDF is up to date
                }
            }

            try
            {
                switch (extension)
                {
                    case ".docx":
                    case ".doc":
                    case ".xlsx":
                    case ".xls":
                    case ".pptx":
                    case ".ppt":
                        // Try LibreOffice first, then fallback to Office Interop, then create placeholder
                        var libreOfficePath = await TryConvertWithLibreOfficeAsync(inputFilePath, outputPath);
                        if (!string.IsNullOrEmpty(libreOfficePath))
                            return libreOfficePath;

                        try
                        {
                            // Fallback to Office Interop
                            return await ConvertWithOfficeInteropAsync(inputFilePath, outputPath, extension);
                        }
                        catch
                        {
                            // Final fallback: return null to trigger SimpleDocumentViewer
                            return null;
                        }

                    case ".pdf":
                        return inputFilePath; // Already PDF

                    default:
                        throw new NotSupportedException($"File type {extension} is not supported for conversion");
                }
            }
            catch (System.Runtime.InteropServices.COMException comEx)
            {
                throw new InvalidOperationException($"Microsoft Office is not installed or not properly configured. COM Error: {comEx.Message}", comEx);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to convert {inputFilePath} to PDF: {ex.Message}", ex);
            }
        }

        private static async System.Threading.Tasks.Task<string?> TryConvertWithLibreOfficeAsync(string inputPath, string outputPath)
        {
            try
            {
                // Common LibreOffice installation paths
                string[] libreOfficePaths = {
                    @"C:\Program Files\LibreOffice\program\soffice.exe",
                    @"C:\Program Files (x86)\LibreOffice\program\soffice.exe",
                    @"C:\Program Files\LibreOffice 7.6\program\soffice.exe",
                    @"C:\Program Files (x86)\LibreOffice 7.6\program\soffice.exe",
                    @"C:\Program Files\LibreOffice 7.5\program\soffice.exe",
                    @"C:\Program Files (x86)\LibreOffice 7.5\program\soffice.exe",
                    "soffice" // Try PATH
                };

                string? libreOfficeExe = null;
                foreach (var path in libreOfficePaths)
                {
                    if (path == "soffice" || File.Exists(path))
                    {
                        libreOfficeExe = path;
                        break;
                    }
                }

                if (libreOfficeExe == null)
                    return null; // LibreOffice not found

                var outputDir = Path.GetDirectoryName(outputPath);
                var arguments = $"--headless --convert-to pdf --outdir \"{outputDir}\" \"{inputPath}\"";

                var processStartInfo = new ProcessStartInfo
                {
                    FileName = libreOfficeExe,
                    Arguments = arguments,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    WorkingDirectory = outputDir
                };

                using (var process = new Process { StartInfo = processStartInfo })
                {
                    process.Start();

                    var output = await process.StandardOutput.ReadToEndAsync();
                    var error = await process.StandardError.ReadToEndAsync();

                    await process.WaitForExitAsync();

                    if (process.ExitCode == 0 && File.Exists(outputPath))
                    {
                        return outputPath;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"LibreOffice conversion failed. Exit code: {process.ExitCode}");
                        System.Diagnostics.Debug.WriteLine($"Output: {output}");
                        System.Diagnostics.Debug.WriteLine($"Error: {error}");
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"LibreOffice conversion error: {ex.Message}");
                return null; // Fallback to Office Interop
            }
        }



        private static async System.Threading.Tasks.Task<string> ConvertWithOfficeInteropAsync(string inputPath, string outputPath, string extension)
        {
            switch (extension)
            {
                case ".docx":
                case ".doc":
                    return await ConvertWordToPdfAsync(inputPath, outputPath);
                case ".xlsx":
                case ".xls":
                    return await ConvertExcelToPdfAsync(inputPath, outputPath);
                default:
                    throw new NotSupportedException($"Office Interop conversion not supported for {extension}");
            }
        }

        private static async System.Threading.Tasks.Task<string> ConvertWordToPdfAsync(string inputPath, string outputPath)
        {
            return await System.Threading.Tasks.Task.Run(() =>
            {
                Microsoft.Office.Interop.Word.Application? wordApp = null;
                Document? doc = null;
                
                try
                {
                    wordApp = new Microsoft.Office.Interop.Word.Application();
                    wordApp.Visible = false;
                    wordApp.DisplayAlerts = WdAlertLevel.wdAlertsNone;
                    
                    doc = wordApp.Documents.Open(inputPath, ReadOnly: true);
                    doc.ExportAsFixedFormat(outputPath, WdExportFormat.wdExportFormatPDF);
                    
                    return outputPath;
                }
                finally
                {
                    try
                    {
                        doc?.Close(WdSaveOptions.wdDoNotSaveChanges);
                        wordApp?.Quit(WdSaveOptions.wdDoNotSaveChanges);
                    }
                    catch { }
                    
                    if (doc != null) Marshal.ReleaseComObject(doc);
                    if (wordApp != null) Marshal.ReleaseComObject(wordApp);
                }
            });
        }

        private static async System.Threading.Tasks.Task<string> ConvertExcelToPdfAsync(string inputPath, string outputPath)
        {
            return await System.Threading.Tasks.Task.Run(() =>
            {
                Microsoft.Office.Interop.Excel.Application? excelApp = null;
                Microsoft.Office.Interop.Excel.Workbook? workbook = null;
                
                try
                {
                    excelApp = new Microsoft.Office.Interop.Excel.Application();
                    excelApp.Visible = false;
                    excelApp.DisplayAlerts = false;
                    
                    workbook = excelApp.Workbooks.Open(inputPath, ReadOnly: true);
                    workbook.ExportAsFixedFormat(
                        Microsoft.Office.Interop.Excel.XlFixedFormatType.xlTypePDF,
                        outputPath);
                    
                    return outputPath;
                }
                finally
                {
                    try
                    {
                        workbook?.Close(false);
                        excelApp?.Quit();
                    }
                    catch { }
                    
                    if (workbook != null) Marshal.ReleaseComObject(workbook);
                    if (excelApp != null) Marshal.ReleaseComObject(excelApp);
                }
            });
        }


    }
}

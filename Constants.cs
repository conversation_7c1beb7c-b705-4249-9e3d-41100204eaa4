﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IntechApplication
{
    public static class Constants
    {
        #region Constants cho băng tải con lăn + belt
        // <PERSON><PERSON> số ma sát 1 vòng bi
        public static readonly double FrictionCoefficientPerBearing = 0.02;
        // Hiệu suất 1 cặp vòng bi
        public static readonly double BearingEfficiency = 0.98;
        // Hiệu suất 1 bộ truyền xích
        public static readonly double ChainTransmissionEfficiency = 0.95;
        // Dải tỉ số truyền
        public static readonly List<double> TransmissionRatios = new()
        {
        1, 5, 10, 12.5, 15, 20, 25, 30, 40, 50, 60, 75, 90, 100, 120, 150, 180, 200, 300
        };

        // Hệ số ma sát
        public static readonly double[] FrictionCoefficients = new double[]
        {
        0.2, 0.3, 0.4, 0.5, 0.6, 0.7
        };

        // Cặp nhông (<PERSON>ộng cơ - <PERSON><PERSON> chủ động)
        public static readonly Dictionary<string, double> GearPairs = new Dictionary<string, double>
        {
            {"23/13", 0.57},
            {"23/15", 0.65},
            {"19/15", 0.79},
            {"15/15", 1.00},
            {"15/19", 1.27},
            {"15/23", 1.53},
            {"13/23", 1.77}
        };

        public static readonly int[] ZeroTo100Kg = new int[]
        {
            76, 89, 114, 141
        };

        public static readonly int[] UpTo100Kg = new int[]
        {
            114, 141, 168
        };

        // Công suất động cơ (kW)
        public static readonly double[] MotorPowersKW = new double[]
        {
        3.0, 2.2, 1.5, 0.75, 0.37, 0.18, 0.12, 0.09, 0.06
        };

        public static readonly Dictionary<double, List<int>> PowerTables = new()
        {
            { 0.06, new List<int> { 60, 76 } },
            { 0.09, new List<int> { 76, 89 } },
            { 0.12, new List<int> { 76, 89 } },
            { 0.18, new List<int> { 76, 89 } },
            { 0.37, new List<int> { 114 } },
            { 0.75, new List<int> { 141 } },
            { 1.50, new List<int> { 141, 168 } },
            { 2.20, new List<int> { 141, 168 } },
            { 3.00, new List<int> { 168 } },
        };

        // Cặp nhông BTCL
        public static readonly Dictionary<string, double> BtclGearPairs = new Dictionary<string, double>
        {
            {"24/14", 1.71},
            {"22/14", 1.57},
            {"20/14", 1.43},
            {"18/14", 1.29},
            {"14/14", 1.00}
        };

        // Nhông B40
        public static readonly double[] SprocketB40 = new double[]
        {
        76.2, 88.9, 101.6, 114.3, 127, 139.7, 152.4, 165.1, 177.8
        };

        // Nhông B50
        public static readonly double[] SprocketB50 = new double[]
        {
        95.25, 111.125, 127, 142.875, 158.75
        };

        // Nhông B60
        public static readonly double[] SprocketB60 = new double[]
        {
        95.25, 114.3, 133.35, 152.4, 171.45
        };

        // Dải đường kính con lăn
        public static readonly int[] RollerDiameters = new int[]
        {
        50, 60, 76
        };
        #endregion

        #region Constants cho băng tải sấy
        public static readonly double m = 1.5; // Trọng lượng riêng của không khí (kg/m3)
        public static readonly double Cp = 1005; // Nhiệt dung riêng của không khí (J/kg.K)
        public static readonly double eta = 0.3; // hiệu suất gia nhiệt 
        #endregion

        #region Constants tính toán lực xilanh
        public static readonly int[] CylinderDiameters = new int[] //Đường kính xilanh (mm)
        {
            4,6,10,12,16,20,25,32,40,50,63,80,100,125,140,160,180,200
        };

        public static readonly double[] SupplyPressures = new double[] //Áp suất cung cấp (Mpa)
        {
            0.3, 0.4, 0.5, 0.6, 0.7
        };

        public static readonly Dictionary<string, double> WorkingCoefficient = new Dictionary<string, double>
        {
            { "Đẩy hoặc kéo vật, nén ép\nKhông sử dụng dẫn hướng", 0.7 },
            { "Đẩy hoặc kéo vật sử dụng dẫn hướng\nMa sát rất nhỏ", 1.0 },
            { "Đẩy hoặc kéo vật theo phương thẳng đứng", 0.5 }
        };
        #endregion
    }

}

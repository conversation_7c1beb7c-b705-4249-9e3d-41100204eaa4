using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;

namespace IntechApplication
{
    public partial class SimpleDocumentViewer : UserControl
    {
        private string? _filePath;

        public SimpleDocumentViewer()
        {
            InitializeComponent();
        }

        public void LoadDocument(string filePath, string title)
        {
            _filePath = filePath;
            DocumentTitle.Text = title;
            
            if (File.Exists(filePath))
            {
                var fileInfo = new FileInfo(filePath);
                
                FileNameText.Text = fileInfo.Name;
                FileSizeText.Text = FormatFileSize(fileInfo.Length);
                LastModifiedText.Text = fileInfo.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss");
                FilePathText.Text = filePath;
                
                // Update conversion status based on file type
                var extension = Path.GetExtension(filePath).ToLower();
                switch (extension)
                {
                    case ".docx":
                    case ".doc":
                        ConversionStatusText.Text = "Word document - Requires Microsoft Word for full preview";
                        break;
                    case ".xlsx":
                    case ".xls":
                        ConversionStatusText.Text = "Excel document - Requires Microsoft Excel for full preview";
                        break;
                    case ".pptx":
                    case ".ppt":
                        ConversionStatusText.Text = "PowerPoint document - Requires Microsoft PowerPoint for full preview";
                        break;
                    case ".pdf":
                        ConversionStatusText.Text = "PDF document - Can be viewed directly";
                        break;
                    default:
                        ConversionStatusText.Text = "Document format may require specific application to view";
                        break;
                }
            }
            else
            {
                FileNameText.Text = "File not found";
                FileSizeText.Text = "-";
                LastModifiedText.Text = "-";
                FilePathText.Text = filePath;
                ConversionStatusText.Text = "File does not exist at the specified location";
            }
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        private void OpenButton_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(_filePath))
            {
                try
                {
                    var processStartInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = _filePath,
                        UseShellExecute = true
                    };
                    System.Diagnostics.Process.Start(processStartInfo);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Cannot open file: {ex.Message}", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void CopyPathButton_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(_filePath))
            {
                try
                {
                    Clipboard.SetText(_filePath);
                    MessageBox.Show("File path copied to clipboard!", "Success", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Cannot copy to clipboard: {ex.Message}", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void OpenFolderButton_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(_filePath))
            {
                try
                {
                    var folderPath = Path.GetDirectoryName(_filePath);
                    if (!string.IsNullOrEmpty(folderPath))
                    {
                        var processStartInfo = new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = "explorer.exe",
                            Arguments = $"/select,\"{_filePath}\"",
                            UseShellExecute = true
                        };
                        System.Diagnostics.Process.Start(processStartInfo);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Cannot open folder: {ex.Message}", "Error", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(_filePath))
            {
                LoadDocument(_filePath, DocumentTitle.Text);
            }
        }
    }
}

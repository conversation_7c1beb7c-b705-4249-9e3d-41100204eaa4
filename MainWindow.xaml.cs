﻿using IntechApplication.CalculationControl;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace IntechApplication
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        public class SearchResultItem
        {
            public string Title { get; set; } = string.Empty;          // <PERSON><PERSON><PERSON> thị trong SearchBox
            public string Description { get; set; } = string.Empty;    // <PERSON>ô tả phụ
            public Type ViewType { get; set; } = typeof(object);         // UserControl tương ứng
        }

        public class DocumentItem
        {
            public string Title { get; set; } = string.Empty;          // Tiêu đề document
            public string Description { get; set; } = string.Empty;    // <PERSON><PERSON> tả document
            public string FilePath { get; set; } = string.Empty;       // Đường dẫn file Word
        }
        // Tab 1: Tính toán
        private string _searchText = "";
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (_searchText != value)
                {
                    _searchText = value;
                    OnPropertyChanged(nameof(SearchText));
                    FilterResults();
                }
            }
        }

        private bool _isSearchResultsVisible;
        public bool IsSearchResultsVisible
        {
            get => _isSearchResultsVisible;
            set
            {
                if (_isSearchResultsVisible != value)
                {
                    _isSearchResultsVisible = value;
                    OnPropertyChanged(nameof(IsSearchResultsVisible));
                }
            }
        }

        // Tab 2: Lựa chọn thiết bị
        private string _deviceSearchText = "";
        public string DeviceSearchText
        {
            get => _deviceSearchText;
            set
            {
                if (_deviceSearchText != value)
                {
                    _deviceSearchText = value;
                    OnPropertyChanged(nameof(DeviceSearchText));
                    FilterDeviceResults();
                }
            }
        }

        private bool _isDeviceResultsVisible;
        public bool IsDeviceResultsVisible
        {
            get => _isDeviceResultsVisible;
            set
            {
                if (_isDeviceResultsVisible != value)
                {
                    _isDeviceResultsVisible = value;
                    OnPropertyChanged(nameof(IsDeviceResultsVisible));
                }
            }
        }

        // Tab 3: Tiêu chuẩn cơ sở
        private string _standardsSearchText = "";
        public string StandardsSearchText
        {
            get => _standardsSearchText;
            set
            {
                if (_standardsSearchText != value)
                {
                    _standardsSearchText = value;
                    OnPropertyChanged(nameof(StandardsSearchText));
                    FilterStandardsResults();
                }
            }
        }

        private bool _isStandardsResultsVisible;
        public bool IsStandardsResultsVisible
        {
            get => _isStandardsResultsVisible;
            set
            {
                if (_isStandardsResultsVisible != value)
                {
                    _isStandardsResultsVisible = value;
                    OnPropertyChanged(nameof(IsStandardsResultsVisible));
                }
            }
        }

        // Tab 4: Bộ kỹ năng mềm
        private string _softSkillsSearchText = "";
        public string SoftSkillsSearchText
        {
            get => _softSkillsSearchText;
            set
            {
                if (_softSkillsSearchText != value)
                {
                    _softSkillsSearchText = value;
                    OnPropertyChanged(nameof(SoftSkillsSearchText));
                    FilterSoftSkillsResults();
                }
            }
        }

        private bool _isSoftSkillsResultsVisible;
        public bool IsSoftSkillsResultsVisible
        {
            get => _isSoftSkillsResultsVisible;
            set
            {
                if (_isSoftSkillsResultsVisible != value)
                {
                    _isSoftSkillsResultsVisible = value;
                    OnPropertyChanged(nameof(IsSoftSkillsResultsVisible));
                }
            }
        }

        // Separate content properties for each tab
        private UserControl? _calculationContent;
        public UserControl? CalculationContent
        {
            get => _calculationContent;
            set
            {
                _calculationContent = value;
                OnPropertyChanged(nameof(CalculationContent));
            }
        }

        private UserControl? _deviceContent;
        public UserControl? DeviceContent
        {
            get => _deviceContent;
            set
            {
                _deviceContent = value;
                OnPropertyChanged(nameof(DeviceContent));
            }
        }

        private UserControl? _standardsContent;
        public UserControl? StandardsContent
        {
            get => _standardsContent;
            set
            {
                _standardsContent = value;
                OnPropertyChanged(nameof(StandardsContent));
            }
        }

        private UserControl? _softSkillsContent;
        public UserControl? SoftSkillsContent
        {
            get => _softSkillsContent;
            set
            {
                _softSkillsContent = value;
                OnPropertyChanged(nameof(SoftSkillsContent));
            }
        }

        // Tab 1: Tính toán
        private ObservableCollection<SearchResultItem> _filteredResults = new();
        public ObservableCollection<SearchResultItem> FilteredResults
        {
            get => _filteredResults;
            set
            {
                _filteredResults = value;
                OnPropertyChanged(nameof(FilteredResults));
            }
        }

        // Tab 2: Lựa chọn thiết bị
        private ObservableCollection<DocumentItem> _filteredDeviceResults = new();
        public ObservableCollection<DocumentItem> FilteredDeviceResults
        {
            get => _filteredDeviceResults;
            set
            {
                _filteredDeviceResults = value;
                OnPropertyChanged(nameof(FilteredDeviceResults));
            }
        }

        // Tab 3: Tiêu chuẩn cơ sở
        private ObservableCollection<DocumentItem> _filteredStandardsResults = new();
        public ObservableCollection<DocumentItem> FilteredStandardsResults
        {
            get => _filteredStandardsResults;
            set
            {
                _filteredStandardsResults = value;
                OnPropertyChanged(nameof(FilteredStandardsResults));
            }
        }

        // Tab 4: Bộ kỹ năng mềm
        private ObservableCollection<DocumentItem> _filteredSoftSkillsResults = new();
        public ObservableCollection<DocumentItem> FilteredSoftSkillsResults
        {
            get => _filteredSoftSkillsResults;
            set
            {
                _filteredSoftSkillsResults = value;
                OnPropertyChanged(nameof(FilteredSoftSkillsResults));
            }
        }
        // Tab 1: Tính toán - Load UserControls
        public List<SearchResultItem> AllData { get; } = new()
        {
            new SearchResultItem { Title = "Tính chọn băng tải con lăn", Description="Tính toán băng tải con lăn", ViewType = typeof(RollerConveyor) },
            new SearchResultItem { Title = "Tính chọn băng tải belt", Description="Tính toán băng tải belt", ViewType = typeof(BeltConveyor) },
            new SearchResultItem { Title = "Băng tải sấy", Description="Tính toán băng tải sấy", ViewType = typeof(DryingConveyor) },
            new SearchResultItem { Title = "Lực xilanh", Description="Tính toán lực xilanh", ViewType = typeof(CylinderForce) },
            new SearchResultItem { Title = "Tính tốc độ băng tải belt", Description="Tính toán tốc độ băng tải belt", ViewType = typeof(ConveyorSpeed) },
            new SearchResultItem { Title = "Tính tốc độ băng tải con lăn", Description="Tính toán tốc độ băng tải con lăn", ViewType = typeof(RollerConveyorSpeed) },
            new SearchResultItem { Title = "Unknown", Description="Trang test", ViewType = typeof(TestingPage) }
        };

        // Tab 2: Lựa chọn thiết bị (Phần II) - Load Word files
        public List<DocumentItem> DeviceSelectionData { get; } = new()
        {
            new DocumentItem { Title = "Tính chọn belt", Description="Hướng dẫn tính chọn belt cho hệ thống", FilePath = "Resources/DeviceSelection/TinhChonBelt.docx" },
            new DocumentItem { Title = "Tính chiều dài belt", Description="Phương pháp tính chiều dài belt", FilePath = "Resources/DeviceSelection/TinhChieuDaiBelt.docx" },
            new DocumentItem { Title = "Tính chọn vật liệu", Description="Lựa chọn vật liệu phù hợp", FilePath = "Resources/DeviceSelection/TinhChonVatLieu.docx" },
            new DocumentItem { Title = "Tổng quan các loại động cơ", Description="Tổng quan về các loại động cơ", FilePath = "Resources/DeviceSelection/TongQuanDongCo.docx" },
            new DocumentItem { Title = "Tổng quan các loại sensor", Description="Các loại sensor trong công nghiệp", FilePath = "Resources/DeviceSelection/TongQuanSensor.docx" },
            new DocumentItem { Title = "Tổng quan các loại xy lanh khí nén", Description="Hệ thống xy lanh khí nén", FilePath = "Resources/DeviceSelection/TongQuanXyLanh.docx" },
            new DocumentItem { Title = "Tổng quan về Vision", Description="Hệ thống thị giác máy tính", FilePath = "Resources/DeviceSelection/TongQuanVision.docx" },
            new DocumentItem { Title = "Tổng quan về Robot", Description="Công nghệ Robot trong sản xuất", FilePath = "Resources/DeviceSelection/TongQuanRobot.docx" }
        };

        // Tab 3: Tiêu chuẩn cơ sở (Phần III) - Load Word files
        public List<DocumentItem> StandardsData { get; } = new()
        {
            new DocumentItem { Title = "Tiêu chuẩn dung sai chung", Description="Tiêu chuẩn về dung sai chung", FilePath = "Resources/Standards/TieuChuanDungSaiChung.docx" },
            new DocumentItem { Title = "Tiêu chuẩn dung sai chân gấp", Description="Tiêu chuẩn dung sai chân gấp", FilePath = "Resources/Standards/TieuChuanDungSaiChanGap.docx" },
            new DocumentItem { Title = "Tiêu chuẩn phối ống trục", Description="Tiêu chuẩn phối ống trục", FilePath = "Resources/Standards/TieuChuanPhoiOngTruc.docx" },
            new DocumentItem { Title = "Tiêu chuẩn con lăn Intech", Description="Tiêu chuẩn con lăn Intech", FilePath = "Resources/Standards/TieuChuanConLanIntech.docx" },
            new DocumentItem { Title = "Tiêu chuẩn màu sơn", Description="Tiêu chuẩn về màu sơn", FilePath = "Resources/Standards/TieuChuanMauSon.docx" },
            new DocumentItem { Title = "Tiêu chuẩn hàn", Description="Tiêu chuẩn kỹ thuật hàn", FilePath = "Resources/Standards/TieuChuanHan.docx" },
            new DocumentItem { Title = "Tiêu chuẩn an toàn", Description="Tiêu chuẩn an toàn lao động", FilePath = "Resources/Standards/TieuChuanAnToan.docx" },
            new DocumentItem { Title = "Tiêu chuẩn bụi lông", Description="Tiêu chuẩn về bụi lông", FilePath = "Resources/Standards/TieuChuanBuiLong.xlsx" }
        };

        // Tab 4: Bộ kỹ năng mềm (Phần IV) - Load Word files
        public List<DocumentItem> SoftSkillsData { get; } = new()
        {
            new DocumentItem { Title = "Hướng dẫn Download một số thiết bị", Description="Hướng dẫn tải xuống thiết bị", FilePath = "Resources/SoftSkills/HuongDanDownload.docx" },
            new DocumentItem { Title = "Hướng dẫn đi khảo sát", Description="Quy trình khảo sát thực địa", FilePath = "Resources/SoftSkills/HuongDanKhaoSat.docx" },
            new DocumentItem { Title = "Hướng dẫn lập kế hoạch", Description="Lập kế hoạch dự án hiệu quả", FilePath = "Resources/SoftSkills/HuongDanLapKeHoach.pptx" },
            new DocumentItem { Title = "Tính bày bản vẽ và tính tế trong thiết kế", Description="Kỹ thuật bày bản vẽ", FilePath = "Resources/SoftSkills/TinhBayBanVe.docx" },
            new DocumentItem { Title = "Kỹ năng giao tiếp và thuyết trình", Description="Phát triển kỹ năng giao tiếp", FilePath = "Resources/SoftSkills/KyNangGiaoTiep.docx" },
            new DocumentItem { Title = "Kỹ năng tìm nguyên nhân gốc rễ 5why ?", Description="Phương pháp 5 Why", FilePath = "Resources/SoftSkills/KyNangTimNguyenNhan.docx" }
        };
        // Tab 1: Tính toán - Load UserControl
        public ICommand SelectItemCommand => new DelegateCommand<SearchResultItem>(item =>
        {
            // Tạo instance UserControl từ ViewType
            if (item?.ViewType != null)
            {
                CalculationContent = (UserControl?)Activator.CreateInstance(item.ViewType);
                // Close search results after selection
                IsSearchResultsVisible = false;
                SearchText = "";
            }
        });

        // Tab 2: Lựa chọn thiết bị - Convert and load PDF
        public ICommand SelectDeviceDocumentCommand => new DelegateCommand<DocumentItem>(async item =>
        {
            if (item != null && !string.IsNullOrEmpty(item.FilePath))
            {
                await LoadDocumentAsPdfAsync(item.FilePath, item.Title, "Device");
                IsDeviceResultsVisible = false;
                DeviceSearchText = item.Title;
            }
        });

        // Tab 3: Tiêu chuẩn cơ sở - Convert and load PDF
        public ICommand SelectStandardsDocumentCommand => new DelegateCommand<DocumentItem>(async item =>
        {
            if (item != null && !string.IsNullOrEmpty(item.FilePath))
            {
                await LoadDocumentAsPdfAsync(item.FilePath, item.Title, "Standards");
                IsStandardsResultsVisible = false;
                StandardsSearchText = item.Title;
            }
        });

        // Tab 4: Bộ kỹ năng mềm - Convert and load PDF
        public ICommand SelectSoftSkillsDocumentCommand => new DelegateCommand<DocumentItem>(async item =>
        {
            if (item != null && !string.IsNullOrEmpty(item.FilePath))
            {
                await LoadDocumentAsPdfAsync(item.FilePath, item.Title, "SoftSkills");
                IsSoftSkillsResultsVisible = false;
                SoftSkillsSearchText = item.Title;
            }
        });
        public ICommand Option1Command => new DelegateCommand(() =>
        {
            try
            {
                var filePath = System.IO.Path.GetFullPath(System.IO.Path.Combine("Resources", "PlansWebinterfaceAMR.docx"));
                if (System.IO.File.Exists(filePath))
                {
                    var guidePage = new Documentation.GuidePage(filePath);
                    guidePage.Show();
                }
                else
                {
                    System.Windows.MessageBox.Show($"File not found: {filePath}", "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error opening file: {ex.Message}", "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        });

        public ICommand Option2Command => new DelegateCommand(() =>
        {
            try
            {
                var filePath = System.IO.Path.GetFullPath(System.IO.Path.Combine("Resources", "PlansWebinterfaceAMR2.docx"));
                if (System.IO.File.Exists(filePath))
                {
                    var guidePage = new Documentation.GuidePage(filePath);
                    guidePage.Show();
                }
                else
                {
                    System.Windows.MessageBox.Show($"File not found: {filePath}", "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error opening file: {ex.Message}", "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        });
        // Tab 1: Tính toán
        private void FilterResults()
        {
            Debug.WriteLine($"FilterResults called. SearchText: '{SearchText}'");
            FilteredResults.Clear();

            // Show all items when search is empty or show filtered results
            var results = string.IsNullOrWhiteSpace(SearchText)
                ? AllData.ToList()
                : AllData.Where(x => x.Title.Contains(SearchText, StringComparison.OrdinalIgnoreCase)).ToList();

            foreach (var item in results)
            {
                FilteredResults.Add(item);
            }
        }

        // Tab 2: Lựa chọn thiết bị
        private void FilterDeviceResults()
        {
            Debug.WriteLine($"FilterDeviceResults called. DeviceSearchText: '{DeviceSearchText}'");
            FilteredDeviceResults.Clear();

            var results = string.IsNullOrWhiteSpace(DeviceSearchText)
                ? DeviceSelectionData.ToList()
                : DeviceSelectionData.Where(x => x.Title.Contains(DeviceSearchText, StringComparison.OrdinalIgnoreCase)).ToList();

            foreach (var item in results)
            {
                FilteredDeviceResults.Add(item);
            }
        }

        // Tab 3: Tiêu chuẩn cơ sở
        private void FilterStandardsResults()
        {
            Debug.WriteLine($"FilterStandardsResults called. StandardsSearchText: '{StandardsSearchText}'");
            FilteredStandardsResults.Clear();

            var results = string.IsNullOrWhiteSpace(StandardsSearchText)
                ? StandardsData.ToList()
                : StandardsData.Where(x => x.Title.Contains(StandardsSearchText, StringComparison.OrdinalIgnoreCase)).ToList();

            foreach (var item in results)
            {
                FilteredStandardsResults.Add(item);
            }
        }

        // Tab 4: Bộ kỹ năng mềm
        private void FilterSoftSkillsResults()
        {
            Debug.WriteLine($"FilterSoftSkillsResults called. SoftSkillsSearchText: '{SoftSkillsSearchText}'");
            FilteredSoftSkillsResults.Clear();

            var results = string.IsNullOrWhiteSpace(SoftSkillsSearchText)
                ? SoftSkillsData.ToList()
                : SoftSkillsData.Where(x => x.Title.Contains(SoftSkillsSearchText, StringComparison.OrdinalIgnoreCase)).ToList();

            foreach (var item in results)
            {
                FilteredSoftSkillsResults.Add(item);
            }
        }

        // Load document as PDF method
        private async Task LoadDocumentAsPdfAsync(string filePath, string title, string tabType)
        {
            try
            {
                Debug.WriteLine($"Loading document as PDF: {filePath}");
                var fullPath = System.IO.Path.GetFullPath(filePath);

                if (!System.IO.File.Exists(fullPath))
                {
                    System.Windows.MessageBox.Show($"File not found: {fullPath}", "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    return;
                }

                // Show loading indicator
                var loadingControl = new UserControl();
                var loadingGrid = new Grid();
                var loadingText = new TextBlock
                {
                    Text = $"Converting {title} to PDF...",
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    FontSize = 16
                };
                loadingGrid.Children.Add(loadingText);
                loadingControl.Content = loadingGrid;

                // Set loading control to appropriate tab
                switch (tabType)
                {
                    case "Device":
                        DeviceContent = loadingControl;
                        break;
                    case "Standards":
                        StandardsContent = loadingControl;
                        break;
                    case "SoftSkills":
                        SoftSkillsContent = loadingControl;
                        break;
                }

                try
                {
                    // Convert to PDF
                    var pdfPath = await OfficeToPdfConverter.ConvertToPdfAsync(fullPath);

                    if (!string.IsNullOrEmpty(pdfPath) && System.IO.File.Exists(pdfPath))
                    {
                        // Create PDF viewer
                        var pdfViewer = new PdfViewerControl();
                        await pdfViewer.LoadPdfAsync(pdfPath);

                        // Set PDF viewer to appropriate tab
                        switch (tabType)
                        {
                            case "Device":
                                DeviceContent = pdfViewer;
                                break;
                            case "Standards":
                                StandardsContent = pdfViewer;
                                break;
                            case "SoftSkills":
                                SoftSkillsContent = pdfViewer;
                                break;
                        }
                    }
                    else
                    {
                        // Conversion failed, show document viewer
                        ShowOfficeNotAvailableMessage(fullPath, title, tabType);
                    }
                }
                catch (InvalidOperationException ex) when (ex.Message.Contains("Microsoft Office"))
                {
                    ShowOfficeNotAvailableMessage(fullPath, title, tabType);
                }
                catch (System.Runtime.InteropServices.COMException)
                {
                    ShowOfficeNotAvailableMessage(fullPath, title, tabType);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Error loading document: {ex.Message}", "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private void ShowOfficeNotAvailableMessage(string filePath, string title, string tabType)
        {
            // Use SimpleDocumentViewer instead of basic message
            var documentViewer = new SimpleDocumentViewer();
            documentViewer.LoadDocument(filePath, title);

            // Set document viewer to appropriate tab
            switch (tabType)
            {
                case "Device":
                    DeviceContent = documentViewer;
                    break;
                case "Standards":
                    StandardsContent = documentViewer;
                    break;
                case "SoftSkills":
                    SoftSkillsContent = documentViewer;
                    break;
            }
        }

        private void OpenWithDefaultApplication(string filePath)
        {
            try
            {
                var processStartInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = filePath,
                    UseShellExecute = true
                };
                System.Diagnostics.Process.Start(processStartInfo);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Cannot open file: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        public MainWindow()
        {
            InitializeComponent();
            DataContext = this; // Set DataContext to enable binding

            // Initialize with all data for all tabs
            FilterResults();
            FilterDeviceResults();
            FilterStandardsResults();
            FilterSoftSkillsResults();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is System.Windows.Controls.TextBox textBox)
            {
                SearchText = textBox.Text;
                Debug.WriteLine($"SearchTextBox_TextChanged: '{SearchText}'");
                FilterResults();
                IsSearchResultsVisible = FilteredResults.Count > 0 && !string.IsNullOrEmpty(SearchText);
            }
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            Debug.WriteLine("SearchTextBox_GotFocus called");
            FilterResults();
            IsSearchResultsVisible = FilteredResults.Count > 0;
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            // Delay hiding to allow ListBox selection
            Dispatcher.BeginInvoke(new Action(() =>
            {
                if (!SearchResultsListBox.IsMouseOver)
                {
                    IsSearchResultsVisible = false;
                }
            }), System.Windows.Threading.DispatcherPriority.Background);
        }

        // Tab 1: Tính toán
        private void SearchResultsListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ListBox listBox && listBox.SelectedItem is SearchResultItem item)
            {
                Debug.WriteLine($"SearchResultsListBox_SelectionChanged: {item.Title}");
                SelectItemCommand.Execute(item);
                IsSearchResultsVisible = false;
                SearchText = "";

                // Clear selection to allow selecting the same item again
                listBox.SelectedItem = null;
            }
        }

        // Tab 2: Lựa chọn thiết bị
        private void DeviceSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is System.Windows.Controls.TextBox textBox)
            {
                DeviceSearchText = textBox.Text;
                Debug.WriteLine($"DeviceSearchTextBox_TextChanged: '{DeviceSearchText}'");
                FilterDeviceResults();
                IsDeviceResultsVisible = FilteredDeviceResults.Count > 0 && !string.IsNullOrEmpty(DeviceSearchText);
            }
        }

        private void DeviceSearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            Debug.WriteLine("DeviceSearchTextBox_GotFocus called");
            FilterDeviceResults();
            IsDeviceResultsVisible = FilteredDeviceResults.Count > 0;
        }

        private void DeviceSearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            Dispatcher.BeginInvoke(new Action(() =>
            {
                if (!SearchResultsListBoxTab2.IsMouseOver)
                {
                    IsDeviceResultsVisible = false;
                }
            }), System.Windows.Threading.DispatcherPriority.Background);
        }

        private void DeviceSearchResultsListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ListBox listBox && listBox.SelectedItem is DocumentItem item)
            {
                Debug.WriteLine($"DeviceSearchResultsListBox_SelectionChanged: {item.Title}");
                SelectDeviceDocumentCommand.Execute(item);
                IsDeviceResultsVisible = false;
                DeviceSearchText = item.Title;

                listBox.SelectedItem = null;
            }
        }

        // Tab 3: Tiêu chuẩn cơ sở
        private void StandardsSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is System.Windows.Controls.TextBox textBox)
            {
                StandardsSearchText = textBox.Text;
                Debug.WriteLine($"StandardsSearchTextBox_TextChanged: '{StandardsSearchText}'");
                FilterStandardsResults();
                IsStandardsResultsVisible = FilteredStandardsResults.Count > 0 && !string.IsNullOrEmpty(StandardsSearchText);
            }
        }

        private void StandardsSearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            Debug.WriteLine("StandardsSearchTextBox_GotFocus called");
            FilterStandardsResults();
            IsStandardsResultsVisible = FilteredStandardsResults.Count > 0;
        }

        private void StandardsSearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            Dispatcher.BeginInvoke(new Action(() =>
            {
                if (!SearchResultsListBoxTab3.IsMouseOver)
                {
                    IsStandardsResultsVisible = false;
                }
            }), System.Windows.Threading.DispatcherPriority.Background);
        }

        private void StandardsSearchResultsListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ListBox listBox && listBox.SelectedItem is DocumentItem item)
            {
                Debug.WriteLine($"StandardsSearchResultsListBox_SelectionChanged: {item.Title}");
                SelectStandardsDocumentCommand.Execute(item);
                IsStandardsResultsVisible = false;
                StandardsSearchText = item.Title;

                listBox.SelectedItem = null;
            }
        }

        // Tab 4: Bộ kỹ năng mềm
        private void SoftSkillsSearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is System.Windows.Controls.TextBox textBox)
            {
                SoftSkillsSearchText = textBox.Text;
                Debug.WriteLine($"SoftSkillsSearchTextBox_TextChanged: '{SoftSkillsSearchText}'");
                FilterSoftSkillsResults();
                IsSoftSkillsResultsVisible = FilteredSoftSkillsResults.Count > 0 && !string.IsNullOrEmpty(SoftSkillsSearchText);
            }
        }

        private void SoftSkillsSearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            Debug.WriteLine("SoftSkillsSearchTextBox_GotFocus called");
            FilterSoftSkillsResults();
            IsSoftSkillsResultsVisible = FilteredSoftSkillsResults.Count > 0;
        }

        private void SoftSkillsSearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            Dispatcher.BeginInvoke(new Action(() =>
            {
                if (!SearchResultsListBoxTab4.IsMouseOver)
                {
                    IsSoftSkillsResultsVisible = false;
                }
            }), System.Windows.Threading.DispatcherPriority.Background);
        }

        private void SoftSkillsSearchResultsListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ListBox listBox && listBox.SelectedItem is DocumentItem item)
            {
                Debug.WriteLine($"SoftSkillsSearchResultsListBox_SelectionChanged: {item.Title}");
                SelectSoftSkillsDocumentCommand.Execute(item);
                IsSoftSkillsResultsVisible = false;
                SoftSkillsSearchText = item.Title;

                listBox.SelectedItem = null;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class InverseBooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Collapsed : Visibility.Visible;
            }
            return Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Collapsed;
            }
            return false;
        }
    }

    public class DelegateCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public DelegateCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter)
        {
            return _canExecute?.Invoke() ?? true;
        }

        public void Execute(object? parameter)
        {
            _execute();
        }
    }

    public class DelegateCommand<T> : ICommand
    {
        private readonly Action<T> _execute;
        private readonly Func<T, bool>? _canExecute;

        public DelegateCommand(Action<T> execute, Func<T, bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter)
        {
            if (parameter is T typedParameter)
            {
                return _canExecute?.Invoke(typedParameter) ?? true;
            }
            return false;
        }

        public void Execute(object? parameter)
        {
            if (parameter is T typedParameter)
            {
                _execute(typedParameter);
            }
        }
    }
}
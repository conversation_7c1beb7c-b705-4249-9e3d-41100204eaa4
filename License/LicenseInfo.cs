using System;
using Newtonsoft.Json;

namespace IntechApplication.License
{
    /// <summary>
    /// Thông tin license được lưu trong file license
    /// </summary>
    public class LicenseInfo
    {
        [JsonProperty("user")]
        public string User { get; set; }

        [JsonProperty("company")]
        public string Company { get; set; }

        [JsonProperty("issueDate")]
        public DateTime IssueDate { get; set; }

        [JsonProperty("expiryDate")]
        public DateTime ExpiryDate { get; set; }

        [JsonProperty("features")]
        public string[] Features { get; set; }

        [JsonProperty("version")]
        public string Version { get; set; }

        [JsonProperty("machineId")]
        public string MachineId { get; set; }

        [JsonProperty("licenseId")]
        public string LicenseId { get; set; }

        /// <summary>
        /// Kiểm tra license có hợp lệ không
        /// </summary>
        public bool IsValid()
        {
            return DateTime.Now <= ExpiryDate && DateTime.Now >= IssueDate;
        }

        /// <summary>
        /// Kiểm tra license có hết hạn không
        /// </summary>
        public bool IsExpired()
        {
            return DateTime.Now > ExpiryDate;
        }

        /// <summary>
        /// Số ngày còn lại của license
        /// </summary>
        public int DaysRemaining()
        {
            if (IsExpired()) return 0;
            return (int)(ExpiryDate - DateTime.Now).TotalDays;
        }

        /// <summary>
        /// Chuyển đổi thành JSON string để ký
        /// </summary>
        public string ToJsonString()
        {
            return JsonConvert.SerializeObject(this, Formatting.None);
        }

        /// <summary>
        /// Tạo LicenseInfo từ JSON string
        /// </summary>
        public static LicenseInfo FromJsonString(string json)
        {
            return JsonConvert.DeserializeObject<LicenseInfo>(json);
        }
    }
}

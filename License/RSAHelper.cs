using System;
using System.Security.Cryptography;
using System.Text;

namespace IntechApplication.License
{
    /// <summary>
    /// Helper class để xử lý RSA encryption/decryption và signing/verification
    /// </summary>
    public static class RSAHelper
    {
        /// <summary>
        /// Tạo cặp RSA key (Public/Private)
        /// </summary>
        public static (string publicKey, string privateKey) GenerateKeyPair(int keySize = 2048)
        {
            using (var rsa = RSA.Create(keySize))
            {
                var publicKey = Convert.ToBase64String(rsa.ExportRSAPublicKey());
                var privateKey = Convert.ToBase64String(rsa.ExportRSAPrivateKey());
                return (publicKey, privateKey);
            }
        }

        /// <summary>
        /// Ký dữ liệu bằng private key
        /// </summary>
        public static string SignData(string data, string privateKeyBase64)
        {
            try
            {
                var privateKeyBytes = Convert.FromBase64String(privateKeyBase64);
                using (var rsa = RSA.Create())
                {
                    rsa.ImportRSAPrivateKey(privateKeyBytes, out _);
                    var dataBytes = Encoding.UTF8.GetBytes(data);
                    var signatureBytes = rsa.SignData(dataBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
                    return Convert.ToBase64String(signatureBytes);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Không thể ký dữ liệu: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Xác minh chữ ký bằng public key
        /// </summary>
        public static bool VerifySignature(string data, string signature, string publicKeyBase64)
        {
            try
            {
                var publicKeyBytes = Convert.FromBase64String(publicKeyBase64);
                var signatureBytes = Convert.FromBase64String(signature);
                
                using (var rsa = RSA.Create())
                {
                    rsa.ImportRSAPublicKey(publicKeyBytes, out _);
                    var dataBytes = Encoding.UTF8.GetBytes(data);
                    return rsa.VerifyData(dataBytes, signatureBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Mã hóa dữ liệu bằng public key
        /// </summary>
        public static string Encrypt(string data, string publicKeyBase64)
        {
            try
            {
                var publicKeyBytes = Convert.FromBase64String(publicKeyBase64);
                using (var rsa = RSA.Create())
                {
                    rsa.ImportRSAPublicKey(publicKeyBytes, out _);
                    var dataBytes = Encoding.UTF8.GetBytes(data);
                    var encryptedBytes = rsa.Encrypt(dataBytes, RSAEncryptionPadding.OaepSHA256);
                    return Convert.ToBase64String(encryptedBytes);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Không thể mã hóa dữ liệu: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Giải mã dữ liệu bằng private key
        /// </summary>
        public static string Decrypt(string encryptedData, string privateKeyBase64)
        {
            try
            {
                var privateKeyBytes = Convert.FromBase64String(privateKeyBase64);
                var encryptedBytes = Convert.FromBase64String(encryptedData);
                
                using (var rsa = RSA.Create())
                {
                    rsa.ImportRSAPrivateKey(privateKeyBytes, out _);
                    var decryptedBytes = rsa.Decrypt(encryptedBytes, RSAEncryptionPadding.OaepSHA256);
                    return Encoding.UTF8.GetString(decryptedBytes);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Không thể giải mã dữ liệu: {ex.Message}", ex);
            }
        }
    }
}

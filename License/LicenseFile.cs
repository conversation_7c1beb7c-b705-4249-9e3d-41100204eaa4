using System;
using Newtonsoft.Json;

namespace IntechApplication.License
{
    /// <summary>
    /// Cấu trúc file license chứa thông tin và chữ ký
    /// </summary>
    public class LicenseFile
    {
        [JsonProperty("licenseData")]
        public string LicenseData { get; set; }

        [JsonProperty("signature")]
        public string Signature { get; set; }

        [JsonProperty("algorithm")]
        public string Algorithm { get; set; } = "RSA-SHA256";

        [JsonProperty("version")]
        public string FileVersion { get; set; } = "1.0";

        /// <summary>
        /// Tạo LicenseFile từ LicenseInfo và chữ ký
        /// </summary>
        public static LicenseFile Create(LicenseInfo licenseInfo, string signature)
        {
            return new LicenseFile
            {
                LicenseData = licenseInfo.ToJsonString(),
                Signature = signature,
                Algorithm = "RSA-SHA256",
                FileVersion = "1.0"
            };
        }

        /// <summary>
        /// Lấy LicenseInfo từ LicenseFile
        /// </summary>
        public LicenseInfo GetLicenseInfo()
        {
            return LicenseInfo.FromJsonString(LicenseData);
        }

        /// <summary>
        /// Chuyển đổi thành JSON string
        /// </summary>
        public string ToJsonString()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }

        /// <summary>
        /// Tạo LicenseFile từ JSON string
        /// </summary>
        public static LicenseFile FromJsonString(string json)
        {
            return JsonConvert.DeserializeObject<LicenseFile>(json);
        }
    }
}

using System;
using System.Management;
using System.Security.Cryptography;
using System.Text;
using System.Linq;

namespace IntechApplication.License
{
    /// <summary>
    /// Class để tạo machine identifier duy nhất cho mỗi máy
    /// </summary>
    public static class MachineIdentifier
    {
        /// <summary>
        /// Lấy Machine ID duy nhất dựa trên hardware của máy
        /// </summary>
        public static string GetMachineId()
        {
            try
            {
                var components = new[]
                {
                    GetProcessorId(),
                    GetMotherboardSerial(),
                    GetBiosSerial(),
                    GetMacAddress()
                };

                var combinedId = string.Join("|", components.Where(c => !string.IsNullOrEmpty(c)));
                
                // Tạo hash SHA256 từ combined ID để có độ dài cố định
                using (var sha256 = SHA256.Create())
                {
                    var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(combinedId));
                    return Convert.ToBase64String(hashBytes);
                }
            }
            catch
            {
                // Fallback: sử dụng Environment.MachineName và Environment.UserName
                var fallbackId = $"{Environment.MachineName}|{Environment.UserName}|{Environment.OSVersion}";
                using (var sha256 = SHA256.Create())
                {
                    var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(fallbackId));
                    return Convert.ToBase64String(hashBytes);
                }
            }
        }

        private static string GetProcessorId()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        return obj["ProcessorId"]?.ToString();
                    }
                }
            }
            catch { }
            return null;
        }

        private static string GetMotherboardSerial()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        return obj["SerialNumber"]?.ToString();
                    }
                }
            }
            catch { }
            return null;
        }

        private static string GetBiosSerial()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BIOS"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        return obj["SerialNumber"]?.ToString();
                    }
                }
            }
            catch { }
            return null;
        }

        private static string GetMacAddress()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT MACAddress FROM Win32_NetworkAdapter WHERE MACAddress IS NOT NULL"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var macAddress = obj["MACAddress"]?.ToString();
                        if (!string.IsNullOrEmpty(macAddress))
                        {
                            return macAddress;
                        }
                    }
                }
            }
            catch { }
            return null;
        }
    }
}

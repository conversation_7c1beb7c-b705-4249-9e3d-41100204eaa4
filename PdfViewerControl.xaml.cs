using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using System.Threading.Tasks;
using PdfiumViewer;
using System.Drawing;
using System.Drawing.Imaging;

namespace IntechApplication
{
    public partial class PdfViewerControl : UserControl
    {
        private PdfDocument? _pdfDocument;
        private int _currentPage = 0;
        private double _zoomFactor = 1.0;
        private string? _pdfFilePath;

        public PdfViewerControl()
        {
            InitializeComponent();
            PageNumberTextBox.TextChanged += PageNumberTextBox_TextChanged;
        }

        public async Task LoadPdfAsync(string pdfFilePath)
        {
            try
            {
                _pdfFilePath = pdfFilePath;
                ShowLoading(true);
                
                await Task.Run(() =>
                {
                    if (_pdfDocument != null)
                    {
                        _pdfDocument.Dispose();
                        _pdfDocument = null;
                    }

                    if (File.Exists(pdfFilePath))
                    {
                        _pdfDocument = PdfDocument.Load(pdfFilePath);
                    }
                });

                if (_pdfDocument != null)
                {
                    _currentPage = 0;
                    _zoomFactor = 1.0;
                    
                    Dispatcher.Invoke(() =>
                    {
                        TotalPagesTextBlock.Text = $"/ {_pdfDocument.PageCount}";
                        PageNumberTextBox.Text = "1";
                        UpdatePageDisplay();
                        ShowLoading(false);
                    });
                }
                else
                {
                    ShowError("Failed to load PDF document");
                }
            }
            catch (Exception ex)
            {
                ShowError($"Error loading PDF: {ex.Message}");
            }
        }

        private void UpdatePageDisplay()
        {
            if (_pdfDocument == null || _currentPage < 0 || _currentPage >= _pdfDocument.PageCount)
                return;

            try
            {
                // Render page to bitmap
                var dpi = (int)(96 * _zoomFactor);
                using (var image = _pdfDocument.Render(_currentPage, dpi, dpi, false))
                {
                    // Convert to BitmapSource for WPF
                    var bitmapSource = ConvertToBitmapSource(image);
                    PdfImage.Source = bitmapSource;
                }

                // Update UI
                PageNumberTextBox.Text = (_currentPage + 1).ToString();
                PrevPageButton.IsEnabled = _currentPage > 0;
                NextPageButton.IsEnabled = _currentPage < _pdfDocument.PageCount - 1;
            }
            catch (Exception ex)
            {
                ShowError($"Error rendering page: {ex.Message}");
            }
        }

        private BitmapSource ConvertToBitmapSource(System.Drawing.Image image)
        {
            using (var memory = new MemoryStream())
            {
                image.Save(memory, ImageFormat.Png);
                memory.Position = 0;
                
                var bitmapImage = new BitmapImage();
                bitmapImage.BeginInit();
                bitmapImage.StreamSource = memory;
                bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                bitmapImage.EndInit();
                bitmapImage.Freeze();
                
                return bitmapImage;
            }
        }

        private void ShowLoading(bool show)
        {
            LoadingGrid.Visibility = show ? Visibility.Visible : Visibility.Collapsed;
            PdfScrollViewer.Visibility = show ? Visibility.Collapsed : Visibility.Visible;
            ErrorGrid.Visibility = Visibility.Collapsed;
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorGrid.Visibility = Visibility.Visible;
            LoadingGrid.Visibility = Visibility.Collapsed;
            PdfScrollViewer.Visibility = Visibility.Collapsed;
        }

        // Event handlers
        private void ZoomInButton_Click(object sender, RoutedEventArgs e)
        {
            _zoomFactor *= 1.25;
            UpdatePageDisplay();
        }

        private void ZoomOutButton_Click(object sender, RoutedEventArgs e)
        {
            _zoomFactor /= 1.25;
            if (_zoomFactor < 0.1) _zoomFactor = 0.1;
            UpdatePageDisplay();
        }

        private void FitToWidthButton_Click(object sender, RoutedEventArgs e)
        {
            _zoomFactor = 1.0;
            UpdatePageDisplay();
        }

        private void PrevPageButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage > 0)
            {
                _currentPage--;
                UpdatePageDisplay();
            }
        }

        private void NextPageButton_Click(object sender, RoutedEventArgs e)
        {
            if (_pdfDocument != null && _currentPage < _pdfDocument.PageCount - 1)
            {
                _currentPage++;
                UpdatePageDisplay();
            }
        }

        private void PageNumberTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (int.TryParse(PageNumberTextBox.Text, out int pageNumber) && _pdfDocument != null)
            {
                pageNumber--; // Convert to 0-based index
                if (pageNumber >= 0 && pageNumber < _pdfDocument.PageCount && pageNumber != _currentPage)
                {
                    _currentPage = pageNumber;
                    UpdatePageDisplay();
                }
            }
        }

        private async void RetryButton_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(_pdfFilePath))
            {
                await LoadPdfAsync(_pdfFilePath);
            }
        }

        private void UserControl_Unloaded(object sender, RoutedEventArgs e)
        {
            _pdfDocument?.Dispose();
        }
    }
}

<UserControl x:Class="IntechApplication.PdfViewerControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IntechApplication"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800"
             Unloaded="UserControl_Unloaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Toolbar -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Background="#F0F0F0" Margin="5">
            <Button Name="ZoomInButton" Content="Zoom In" Margin="5" Padding="10,5" Click="ZoomInButton_Click"/>
            <Button Name="ZoomOutButton" Content="Zoom Out" Margin="5" Padding="10,5" Click="ZoomOutButton_Click"/>
            <Button Name="FitToWidthButton" Content="Fit Width" Margin="5" Padding="10,5" Click="FitToWidthButton_Click"/>
            <TextBlock Text="Page:" VerticalAlignment="Center" Margin="10,0,5,0"/>
            <TextBox Name="PageNumberTextBox" Width="50" Margin="0,0,5,0" VerticalAlignment="Center" TextAlignment="Center"/>
            <TextBlock Name="TotalPagesTextBlock" Text="/ 0" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <Button Name="PrevPageButton" Content="Previous" Margin="5" Padding="10,5" Click="PrevPageButton_Click"/>
            <Button Name="NextPageButton" Content="Next" Margin="5" Padding="10,5" Click="NextPageButton_Click"/>
        </StackPanel>
        
        <!-- PDF Viewer -->
        <ScrollViewer Grid.Row="1" Name="PdfScrollViewer" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto">
            <Image Name="PdfImage" Stretch="Uniform"/>
        </ScrollViewer>
        
        <!-- Loading indicator -->
        <Grid Grid.Row="1" Name="LoadingGrid" Background="White" Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="200" Height="20" Margin="10"/>
                <TextBlock Text="Loading PDF..." HorizontalAlignment="Center" Margin="10"/>
            </StackPanel>
        </Grid>
        
        <!-- Error message -->
        <Grid Grid.Row="1" Name="ErrorGrid" Background="White" Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Name="ErrorTextBlock" Text="Error loading PDF" HorizontalAlignment="Center" Foreground="Red" FontSize="16" Margin="10"/>
                <Button Name="RetryButton" Content="Retry" Margin="10" Padding="20,10" Click="RetryButton_Click"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>

﻿<UserControl x:Class="IntechApplication.CalculationControl.RollerConveyorSpeed"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:IntechApplication.CalculationControl"
             xmlns:mD="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <TextBlock Text="Tính toán tốc độ băng tải con lăn" 
 FontSize="24" 
 FontWeight="Bold" 
 HorizontalAlignment="Center" 
 VerticalAlignment="Top" 
 Margin="10"/>
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="10" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="20" />
                <ColumnDefinition Width="2*" />
            </Grid.ColumnDefinitions>
            <!-- Image Placeholder -->
            <GroupBox Header="Mô hình"
Grid.Column="1"
Margin="0,0,0,10"
mD:ElevationAssist.Elevation="Dp6"
BorderBrush="DarkRed" BorderThickness="1">
                <Image Source="pack://application:,,,/IntechApplication;component/Resources/Images/conveyorSpeed.png"/>
            </GroupBox>
            <Grid Grid.Column="3">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Input Parameters-->
                <GroupBox Header="Động cơ cốt dương"
    mD:ElevationAssist.Elevation="Dp6" Margin="0,0,10,5"
    BorderBrush="DarkRed" BorderThickness="1">
                    <Grid Margin="10" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*" />
                            <ColumnDefinition Width="20" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!-- Labels -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Height="25" Text="Số vòng quay của motor(f0)"/>
                            <TextBlock Height="25" Text="Tỉ số truyền hộp số ĐC(i)" />
                            <TextBlock Height="25" Text="Cặp nhông động cơ(Z1)" />
                            <TextBlock Height="25" Text="Cặp nhông lô động cơ(Z2)" />
                            <TextBlock Height="25" Text="Đường kính con lăn(D)" />
                            <TextBlock Height="25" Text="Tần số" />
                            <TextBlock Height="25" FontWeight="Bold" Text="Vận tốc băng tải" />
                        </StackPanel>
                        <!-- Fields -->
                        <StackPanel Grid.Column="2">
                            <TextBox Height="25" Text="{Binding PositiveRollerMotorSpeed}"/>
                            <TextBox Height="25" Text="{Binding PositiveRollerTransmissionRatio}"/>
                            <TextBox Height="25" Text="{Binding PositiveRollerMotorSprocket}"/>
                            <TextBox Height="25" Text="{Binding PositiveRollerDrivenSprocket}"/>
                            <ComboBox Height="25" ItemsSource="{Binding PositiveRollerDrivenRollerDiameterOptions}" SelectedItem="{Binding SelectedPositiveRollerDrivenRollerDiameter}"/>
                            <TextBox Height="25" Text="{Binding PositiveRollerFrequency}"/>
                            <TextBlock Height="25" Text="{Binding PositiveRollerConveyorSpeed}"/>
                        </StackPanel>
                        <!-- Units -->
                        <StackPanel Grid.Column="3" Margin="10,0,0,0">
                            <TextBlock Height="25" Text="Vòng trên phút"/>
                            <TextBlock Height="25" />
                            <TextBlock Height="25" Text="Răng"/>
                            <TextBlock Height="25" Text="Răng"/>
                            <TextBlock Height="25" Text="mm" />
                            <TextBlock Height="25" Text="Hz" />
                            <TextBlock FontWeight="Bold" Height="25" Text="m/ph" />
                        </StackPanel>
                    </Grid>
                </GroupBox>
                <!-- Output -->
                <GroupBox Header="Tiêu chuẩn Intech" Grid.Row="1" Margin="0,5,10,10" Padding="0"
    mD:ElevationAssist.Elevation="Dp6"
    BorderBrush="DarkRed" BorderThickness="1">
                    <StackPanel>
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <!-- Labels Positive-->
                            <StackPanel Grid.Column="0">
                                <TextBlock Background="Gold" TextAlignment="Center" FontSize="15" Height="25" Text="Đường kính con lăn" FontWeight="Bold"/>
                                <TextBlock Background="AliceBlue" TextAlignment="Center" Height="25" Text="D50" />
                                <TextBlock Background="AliceBlue" TextAlignment="Center" Height="25" Text="D60" />
                                <TextBlock Background="AliceBlue" TextAlignment="Center" Height="25" Text="D76" />
                            </StackPanel>
                            <!-- Units Positive-->
                            <StackPanel Grid.Column="1">
                                <TextBlock Background="Gold" TextAlignment="Center" FontSize="15" Height="25" Text="Số răng" FontWeight="Bold"/>
                                <TextBlock Background="AliceBlue" TextAlignment="Center" Height="25" Text="14" />
                                <TextBlock Background="AliceBlue" TextAlignment="Center" Height="25" Text="14" />
                                <TextBlock Background="AliceBlue" TextAlignment="Center" Height="25" Text="13" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </GroupBox>
            </Grid>
        </Grid>
    </Grid>
</UserControl>

﻿<UserControl x:Class="IntechApplication.CalculationControl.ConveyorSpeed"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:IntechApplication.CalculationControl"
             xmlns:mD="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="900" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <TextBlock Text="Tính toán tốc độ băng tải belt" 
   FontSize="24" 
   FontWeight="Bold" 
   HorizontalAlignment="Center" 
   VerticalAlignment="Top" 
   Margin="10"/>
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="10" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="20" />
                <ColumnDefinition Width="2*" />
            </Grid.ColumnDefinitions>
            <!-- Image Placeholder -->
            <GroupBox Header="Mô hình"
  Grid.Column="1"
  Margin="0,0,0,10"
  mD:ElevationAssist.Elevation="Dp6"
  BorderBrush="DarkRed" BorderThickness="1">
                <Image Source="pack://application:,,,/IntechApplication;component/Resources/Images/BeltConveyorRMBG.png"/>
            </GroupBox>
            <Grid Grid.Column="3">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Input Parameters-->
                <GroupBox Header="Công thức"
      mD:ElevationAssist.Elevation="Dp6" Margin="0,0,10,5"
      BorderBrush="DarkRed" BorderThickness="1">
                    <TabControl>
                        <TabItem Header="Động cơ cốt dương">
                            <Grid Margin="10" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*" />
                                    <ColumnDefinition Width="20" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <!-- Labels -->
                                <StackPanel Grid.Column="0">
                                    <TextBlock Height="25" Text="Số vòng quay của motor(f0)"/>
                                    <TextBlock Height="25" Text="Tỉ số truyền hộp số ĐC(i)" />
                                    <TextBlock Height="25" Text="Cặp nhông động cơ(Z1)" />
                                    <TextBlock Height="25" Text="Cặp nhông lô động cơ(Z2)" />
                                    <TextBlock Height="25" Text="Đường kính lô CĐ(D)" />
                                    <TextBlock Height="25" Text="Chiều dày dây belt(x)" />
                                    <TextBlock Height="25" Text="Tần số" />
                                    <TextBlock Height="25" FontWeight="Bold" Text="Vận tốc băng tải" />
                                </StackPanel>
                                <!-- Fields -->
                                <StackPanel Grid.Column="2">
                                    <TextBox Height="25" Text="{Binding PositiveMotorSpeed}"/>
                                    <TextBox Height="25" Text="{Binding PositiveTransmissionRatio}"/>
                                    <TextBox Height="25" Text="{Binding PositiveMotorSprocket}"/>
                                    <TextBox Height="25" Text="{Binding PositiveDrivenSprocket}"/>
                                    <TextBox Height="25" Text="{Binding PositiveDrivenRollerDiameter}"/>
                                    <TextBox Height="25" Text="{Binding PositiveBeltLength}"/>
                                    <TextBox Height="25" Text="{Binding PositiveFrequency}"/>
                                    <TextBlock Height="25" Text="{Binding PositiveConveyorSpeed}"/>
                                </StackPanel>
                                <!-- Units -->
                                <StackPanel Grid.Column="3" Margin="10,0,0,0">
                                    <TextBlock Height="25" Text="Vòng trên phút"/>
                                    <TextBlock Height="25" />
                                    <TextBlock Height="25" Text="Răng"/>
                                    <TextBlock Height="25" Text="Răng"/>
                                    <TextBlock Height="25" Text="mm" />
                                    <TextBlock Height="25" Text="mm" />
                                    <TextBlock Height="25" Text="Hz" />
                                    <TextBlock FontWeight="Bold" Height="25" Text="m/ph" />
                                </StackPanel>
                            </Grid>
                        </TabItem>
                        <TabItem Header="Động cơ cốt ấm">
                            <Grid Margin="10" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*" />
                                    <ColumnDefinition Width="20" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <!-- Labels -->
                                <StackPanel Grid.Column="0">
                                    <TextBlock Height="25" Text="Số vòng quay của motor(f0)"/>
                                    <TextBlock Height="25" Text="Tỉ số truyền hộp số ĐC(i)" />
                                    <TextBlock Height="25" Text="Đường kính lô CĐ(D)" />
                                    <TextBlock Height="25" Text="Chiều dày dây belt(x)" />
                                    <TextBlock Height="25" Text="Tần số" />
                                    <TextBlock Height="25" FontWeight="Bold" Text="Vận tốc băng tải" />
                                </StackPanel>
                                <!-- Fields -->
                                <StackPanel Grid.Column="2">
                                    <TextBox Height="25" Text="{Binding NegativeMotorSpeed}"/>
                                    <TextBox Height="25" Text="{Binding NegativeTransmissionRatio}"/>
                                    <TextBox Height="25" Text="{Binding NegativeDrivenRollerDiameter}"/>
                                    <TextBox Height="25" Text="{Binding NegativeBeltLength}"/>
                                    <TextBox Height="25" Text="{Binding NegativeFrequency}"/>
                                    <TextBlock Height="25" Text="{Binding NegativeConveyorSpeed}"/>
                                </StackPanel>
                                <!-- Units -->
                                <StackPanel Grid.Column="3" Margin="10,0,0,0">
                                    <TextBlock Height="25" Text="Vòng trên phút"/>
                                    <TextBlock Height="25" />
                                    <TextBlock Height="25" Text="mm" />
                                    <TextBlock Height="25" Text="mm" />
                                    <TextBlock Height="25" Text="Hz" />
                                    <TextBlock FontWeight="Bold" Height="25" Text="m/ph" />
                                </StackPanel>
                            </Grid>
                        </TabItem>
                    </TabControl>
                </GroupBox>
                <!-- Output -->
                <GroupBox Header="Tiêu chuẩn Intech" Grid.Row="1" Margin="0,5,10,10" Padding="0"
      mD:ElevationAssist.Elevation="Dp6"
      BorderBrush="DarkRed" BorderThickness="1">
                    <StackPanel>
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <!-- Labels Positive-->
                            <StackPanel Grid.Column="0">
                                <TextBlock Background="Gold" TextAlignment="Center" FontSize="15" Height="25" Text="Đường kính con lăn" FontWeight="Bold"/>
                                <TextBlock Background="AliceBlue" TextAlignment="Center" Height="25" Text="D50" />
                                <TextBlock Background="AliceBlue" TextAlignment="Center" Height="25" Text="D60" />
                                <TextBlock Background="AliceBlue" TextAlignment="Center" Height="25" Text="D76" />
                            </StackPanel>
                            <!-- Units Positive-->
                            <StackPanel Grid.Column="1">
                                <TextBlock Background="Gold" TextAlignment="Center" FontSize="15" Height="25" Text="Số răng" FontWeight="Bold"/>
                                <TextBlock Background="AliceBlue" TextAlignment="Center" Height="25" Text="14" />
                                <TextBlock Background="AliceBlue" TextAlignment="Center" Height="25" Text="14" />
                                <TextBlock Background="AliceBlue" TextAlignment="Center" Height="25" Text="13" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </GroupBox>
            </Grid>
        </Grid>
    </Grid>
</UserControl>

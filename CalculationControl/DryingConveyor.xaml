﻿<UserControl x:Class="IntechApplication.CalculationControl.DryingConveyor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:IntechApplication.CalculationControl"
             xmlns:mD="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="900" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <TextBlock Text="Tính toán băng tải sấy" 
           FontSize="24" 
           FontWeight="Bold" 
           HorizontalAlignment="Center" 
           VerticalAlignment="Top" 
           Margin="10"/>
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="10" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="20" />
                <ColumnDefinition Width="2*" />
            </Grid.ColumnDefinitions>
            <!-- Image Placeholder -->
            <GroupBox Header="Mô hình"
          Grid.Column="1"
          Margin="0,0,0,10"
          mD:ElevationAssist.Elevation="Dp6"
          BorderBrush="DarkRed" BorderThickness="1">
                <Image Source="pack://application:,,,/IntechApplication;component/Resources/Images/BeltConveyorRMBG.png"/>
            </GroupBox>
            <Grid Grid.Column="3">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Input Parameters-->
                <GroupBox Header="Đầu vào"
              mD:ElevationAssist.Elevation="Dp6" Margin="0,0,10,5"
              BorderBrush="DarkRed" BorderThickness="1">
                    <Grid Margin="10" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*" />
                            <ColumnDefinition Width="20" />
                            <ColumnDefinition Width="100" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!-- Labels -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Height="25" Text="Chiều dài buồng sấy"/>
                            <TextBlock Height="25" Text="Chiều rộng buồng sấy"/>
                            <TextBlock Height="25" Text="Chiều cao buồng sấy"/>
                            <TextBlock Height="25" Text="Nhiệt độ ban đầu" />
                            <TextBlock Height="25" Text="Nhiệt độ mong muốn" />
                            <TextBlock Height="25" Text="Thời gian giới hạn đạt nhiệt độ đích" />
                            <TextBlock Height="25" Text="Công suất bóng sấy" />
                        </StackPanel>
                        <!-- Fields -->
                        <StackPanel Grid.Column="2">
                            <TextBox Height="25" Text="{Binding ConveyorLength}"/>
                            <TextBox Height="25" Text="{Binding ConveyorWidth}"/>
                            <TextBox Height="25" Text="{Binding ConveyorHeight}"/>
                            <TextBox Height="25" Text="{Binding StartTemp}"/>
                            <TextBox Height="25" Text="{Binding GoalTemp}"/>
                            <TextBox Height="25" Text="{Binding LimitTimeToReach}"/>
                            <TextBox Height="25" Text="{Binding CapaPerHalogen}"/>
                        </StackPanel>
                        <!-- Units -->
                        <StackPanel Grid.Column="3" Margin="10,0,0,0">
                            <TextBlock Padding="5" Height="25" Text="m"/>
                            <TextBlock Padding="5" Height="25" Text="m" />
                            <TextBlock Padding="5" Height="25" Text="m" />
                            <TextBlock Padding="5" Height="25" Text="ºC" />
                            <TextBlock Padding="5" Height="25" Text="ºC" />
                            <TextBlock Padding="5" Height="25" Text="s" />
                            <TextBlock Padding="5" Height="25" Text="W" />
                        </StackPanel>
                    </Grid>
                </GroupBox>
                <!-- Output -->
                <GroupBox Header="Đầu ra" Grid.Row="1" Margin="0,5,10,10" Padding="0"
              mD:ElevationAssist.Elevation="Dp6"
              BorderBrush="DarkRed" BorderThickness="1">
                    <StackPanel>
                        <Grid Margin="10" VerticalAlignment="Center" HorizontalAlignment="Center">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*" />
                                <ColumnDefinition Width="20" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <!-- Labels Positive-->
                            <StackPanel Grid.Column="0">
                                <TextBlock Height="25" Text="Thể tích buồng sấy"/>
                                <TextBlock Height="25" Text="Tổng công suất tỏa nhiệt" />
                                <TextBlock Height="25" Text="Tổng bóng halogel cần thiết" />
                            </StackPanel>
                            <!-- Fields Positive-->
                            <StackPanel Grid.Column="2">
                                <TextBlock Height="25" Text="{Binding DryingChamberVolume, StringFormat={}{0:F2}}">
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                                <TextBlock Height="25" Text="{Binding TotalCapa}">
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                                <TextBlock Height="25" Text="{Binding NumberHalogel}">
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                            </StackPanel>
                            <!-- Units Positive-->
                            <StackPanel Grid.Column="3" Margin="10,0,0,0">
                                <TextBlock Height="25" Text="m³"/>
                                <TextBlock Height="25" Text="kW"/>
                                <TextBlock Height="25" Text="bóng"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </GroupBox>
            </Grid>
        </Grid>
    </Grid>
</UserControl>

﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace IntechApplication.CalculationControl
{
    /// <summary>
    /// Interaction logic for DryingConveyor.xaml
    /// </summary>
    public partial class DryingConveyor : UserControl, INotifyPropertyChanged
    {
        // Properties for binding to the UI
        private double _conveyorLength = 0.0;
        public double ConveyorLength
        {
            get { return _conveyorLength; }
            set
            {
                _conveyorLength = value;
                // Notify property changed if using INotifyPropertyChanged
                OnPropertyChanged(nameof(DryingChamberVolume));
                OnPropertyChanged(nameof(TotalCapa));
                OnPropertyChanged(nameof(NumberHalogel));
            }
        }
        private double _conveyorWidth = 0.0;
        public double ConveyorWidth
        {
            get { return _conveyorWidth; }
            set
            {
                _conveyorWidth = value;
                // Notify property changed if using INotifyPropertyChanged
                OnPropertyChanged(nameof(DryingChamberVolume));
                OnPropertyChanged(nameof(TotalCapa));
                OnPropertyChanged(nameof(NumberHalogel));
            }
        }
        private double _conveyorHeight = 0.0;
        public double ConveyorHeight
        {
            get { return _conveyorHeight; }
            set
            {
                _conveyorHeight = value;
                // Notify property changed if using INotifyPropertyChanged
                OnPropertyChanged(nameof(DryingChamberVolume));
                OnPropertyChanged(nameof(TotalCapa));
                OnPropertyChanged(nameof(NumberHalogel));
            }
        }
        public double DryingChamberVolume
        {
            get
            {
                return ConveyorLength * ConveyorWidth * ConveyorHeight;
            }
        }
        private double _startTemp = 0.0;
        public double StartTemp
        {
            get { return _startTemp; }
            set
            {
                _startTemp = value;
                OnPropertyChanged(nameof(TotalCapa));
                OnPropertyChanged(nameof(NumberHalogel));
            }
        }
        private double _goalTemp = 0.0;
        public double GoalTemp
        {
            get { return _goalTemp; }
            set
            {
                _goalTemp = value;
                OnPropertyChanged(nameof(TotalCapa));
                OnPropertyChanged(nameof(NumberHalogel));
            }
        }
        private double _limitTimeToReach = 0.0;
        public double LimitTimeToReach
        {
            get { return _limitTimeToReach; }
            set
            {
                _limitTimeToReach = value;
                OnPropertyChanged(nameof(TotalCapa));
                OnPropertyChanged(nameof(NumberHalogel));
            }
        }
        private double _capaPerHalogen = 0.0;
        public double CapaPerHalogen
        {
            get { return _capaPerHalogen; }
            set
            {
                _capaPerHalogen = value;
                OnPropertyChanged(nameof(NumberHalogel));
            }
        }
        public int TotalCapa
        {
            get
            {
                // Tính toán theo công thức
                double numerator = DryingChamberVolume * Constants.m * Constants.Cp * (GoalTemp - StartTemp);
                double denominator = LimitTimeToReach * Constants.eta;

                // Tránh chia cho 0
                if (denominator == 0)
                {
                    return 0;
                }

                double result = numerator / denominator;
                return (int)Math.Ceiling(result); // Làm tròn lên bằng Math.Ceiling
            }
        }
        public int NumberHalogel
        {
            get
            {
                // Tính toán số lượng halogen cần thiết
                if (CapaPerHalogen == 0)
                {
                    return 0; // Tránh chia cho 0
                }
                return (int)Math.Ceiling((double)TotalCapa / CapaPerHalogen);
            }
        }
        public DryingConveyor()
        {
            InitializeComponent();
            DataContext = this;
        }
        private void CopyText_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuItem;
            var textBlock = (menuItem?.Parent as ContextMenu)?.PlacementTarget as TextBlock;

            if (textBlock != null)
            {
                Clipboard.SetText(textBlock.Text);
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}

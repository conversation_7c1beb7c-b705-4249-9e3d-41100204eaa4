﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace IntechApplication.CalculationControl
{
    /// <summary>
    /// Interaction logic for ConveyorSpeed.xaml
    /// </summary>
    public partial class ConveyorSpeed : UserControl, INotifyPropertyChanged
    {
        //Positive Conveyor Speed Properties
        // Số vòng quay của motor
        private double _positiveMotorSpeed = 1;
        public double PositiveMotorSpeed // f0
        {
            get { return _positiveMotorSpeed; }
            set
            {
                _positiveMotorSpeed = value;
                OnPropertyChanged(nameof(PositiveMotorSpeed));
                OnPropertyChanged(nameof(PositiveConveyorSpeed)); // Update ConveyorSpeed when MotorSpeed changes
            }
        }
        //Tỷ số truyền
        private double _positiveTransmissionRatio = 1.0;
        public double PositiveTransmissionRatio // i
        {
            get { return _positiveTransmissionRatio; }
            set
            {
                _positiveTransmissionRatio = value;
                OnPropertyChanged(nameof(PositiveTransmissionRatio));
                OnPropertyChanged(nameof(PositiveConveyorSpeed)); // Update ConveyorSpeed when TransmissionRatio changes
            }
        }
        // Cặp nhông động cơ 
        private int _positiveMotorSprocket = 1;
        public int PositiveMotorSprocket // Z1
        {
            get { return _positiveMotorSprocket; }
            set
            {
                _positiveMotorSprocket = value;
                OnPropertyChanged(nameof(_positiveMotorSprocket));
                OnPropertyChanged(nameof(PositiveConveyorSpeed)); // Update ConveyorSpeed when MotorSprocket changes
            }
        }
        // Cặp nhông lô chủ động
        private int _positiveDrivenSprocket = 1;
        public int PositiveDrivenSprocket // Z2
        {
            get { return _positiveDrivenSprocket; }
            set
            {
                _positiveDrivenSprocket = value;
                OnPropertyChanged(nameof(PositiveDrivenSprocket));
                OnPropertyChanged(nameof(PositiveConveyorSpeed)); // Update ConveyorSpeed when DrivenSprocket changes
            }
        }
        // Đường kính lô chủ động 
        private int _positiveDrivenRollerDiameter = 50;
        public int PositiveDrivenRollerDiameter // D
        {
            get { return _positiveDrivenRollerDiameter; }
            set
            {
                _positiveDrivenRollerDiameter = value;
                OnPropertyChanged(nameof(PositiveDrivenRollerDiameter));
                OnPropertyChanged(nameof(PositiveConveyorSpeed)); // Update ConveyorSpeed when DrivenRollerDiameter changes
            }
        }
        // Chiều dài dây belt
        private double _positiveBeltLength = 1.0;
        public double PositiveBeltLength // x
        {
            get { return _positiveBeltLength; }
            set
            {
                _positiveBeltLength = value;
                OnPropertyChanged(nameof(PositiveBeltLength));
                OnPropertyChanged(nameof(PositiveConveyorSpeed)); // Update ConveyorSpeed when BeltLength changes
            }
        }
        // Tần số
        private double _positiveFrequency = 50.0;
        public double PositiveFrequency
        { 
            get { return _positiveFrequency; }
            set
            {
                _positiveFrequency = value;
                OnPropertyChanged(nameof(PositiveFrequency));
                OnPropertyChanged(nameof(PositiveConveyorSpeed)); // Update ConveyorSpeed when Frequency changes
            }
        }
        // Vận tốc băng tải = 𝜋.(𝐷+2𝑥).  𝑍_1/𝑍_2 .𝑓_0/𝑖    〖(𝑚〗∕𝑝ℎ)
        public double PositiveConveyorSpeed
        {
            get
            {
                // Calculate the conveyor speed based on the properties
                double diameter = (PositiveDrivenRollerDiameter + 2 * PositiveBeltLength) / 1000.0; // Convert mm to m
                double motorSpeed = PositiveMotorSpeed/ PositiveTransmissionRatio;
                double sprocketRatio = (double)PositiveMotorSprocket / PositiveDrivenSprocket;
                return Math.PI * diameter * sprocketRatio * motorSpeed * (PositiveFrequency/50);
            }
        }

        //Negative Conveyor Speed Properties
        // Số vòng quay của motor
        private double _negativeMotorSpeed = 1;
        public double NegativeMotorSpeed // f0
        {
            get { return _negativeMotorSpeed; }
            set
            {
                _negativeMotorSpeed = value;
                OnPropertyChanged(nameof(NegativeMotorSpeed));
                OnPropertyChanged(nameof(NegativeConveyorSpeed)); // Update ConveyorSpeed when MotorSpeed changes
            }
        }
        //Tỷ số truyền
        private double _negativeTransmissionRatio = 1.0;
        public double NegativeTransmissionRatio // i
        {
            get { return _negativeTransmissionRatio; }
            set
            {
                _negativeTransmissionRatio = value;
                OnPropertyChanged(nameof(NegativeTransmissionRatio));
                OnPropertyChanged(nameof(NegativeConveyorSpeed)); // Update ConveyorSpeed when TransmissionRatio changes
            }
        }
        // Đường kính lô chủ động
        private int _negativeDrivenRollerDiameter = 50;
        public int NegativeDrivenRollerDiameter // D
        {
            get { return _negativeDrivenRollerDiameter; }
            set
            {
                _negativeDrivenRollerDiameter = value;
                OnPropertyChanged(nameof(NegativeDrivenRollerDiameter));
                OnPropertyChanged(nameof(NegativeConveyorSpeed)); // Update ConveyorSpeed when DrivenRollerDiameter changes
            }
        }
        // Chiều dài dây belt
        private double _negativeBeltLength = 1.0;
        public double NegativeBeltLength // x
        {
            get { return _negativeBeltLength; }
            set
            {
                _negativeBeltLength = value;
                OnPropertyChanged(nameof(NegativeBeltLength));
                OnPropertyChanged(nameof(NegativeConveyorSpeed)); // Update ConveyorSpeed when BeltLength changes
            }
        }
        // Tần số
        private double _negativeFrequency = 50.0;
        public double NegativeFrequency
        {
            get { return _negativeFrequency; }
            set
            {
                _negativeFrequency = value;
                OnPropertyChanged(nameof(NegativeFrequency));
                OnPropertyChanged(nameof(NegativeConveyorSpeed)); // Update ConveyorSpeed when Frequency changes
            }
        }
        // Vận tốc băng tải = 𝜋.(𝐷+2𝑥).  𝑍_1/𝑍_2 .𝑓_0/𝑖    〖(𝑚〗∕𝑝ℎ)
        public double NegativeConveyorSpeed
        {
            get
            {
                // Calculate the conveyor speed based on the properties
                double diameter = (NegativeDrivenRollerDiameter + 2 * NegativeBeltLength) / 1000.0; // Convert mm to m
                double motorSpeed = NegativeMotorSpeed / NegativeTransmissionRatio;
                return Math.PI * diameter * motorSpeed * (NegativeFrequency / 50);
            }
        }
        public ConveyorSpeed()
        {
            InitializeComponent();
            DataContext = this;
        }

        // PropertyChanged event handler
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}

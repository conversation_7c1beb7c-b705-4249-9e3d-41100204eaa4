﻿<UserControl x:Class="IntechApplication.CalculationControl.RollerConveyor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:IntechApplication.CalculationControl"
             xmlns:mD="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="900" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <TextBlock Text="Tính toán băng tải con lăn" 
               FontSize="24" 
               FontWeight="Bold" 
               HorizontalAlignment="Center" 
               VerticalAlignment="Top" 
               Margin="10"/>
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="10" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="20" />
                <ColumnDefinition Width="2*" />
            </Grid.ColumnDefinitions>
            <!-- Image Placeholder -->
            <GroupBox Header="Mô hình"
              Grid.Column="1"
              Margin="0,0,0,10"
              mD:ElevationAssist.Elevation="Dp6"
              BorderBrush="DarkRed" BorderThickness="1">
                <Image Source="pack://application:,,,/IntechApplication;component/Resources/Images/BeltConveyorRMBG.png"/>
            </GroupBox>
            <Grid Grid.Column="3">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Input Parameters-->
                <GroupBox Header="Đầu vào"
                  mD:ElevationAssist.Elevation="Dp6" Margin="0,0,10,5"
                  BorderBrush="DarkRed" BorderThickness="1">
                    <Grid Margin="10" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*" />
                            <ColumnDefinition Width="20" />
                            <ColumnDefinition Width="100" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!-- Labels -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Height="25" Text="Chiều dài băng tải (L1)" VerticalAlignment="Center"/>
                            <TextBlock Height="25" Text="Chiều rộng băng tải (W)" />
                            <TextBlock Height="25" Text="Đường kính con lăn (D)" />
                            <TextBlock Height="25" Text="Khối lượng sản phẩm (P)" />
                            <TextBlock Height="25" Text="Tổng trọng tải (m)" />
                            <TextBlock Height="25" Text="Khoảng cách tâm con lăn (Nhông B40)" />
                            <TextBlock Height="25" Text="Tốc độ sản phẩm (v)" />
                            <TextBlock Height="25" Text="Vị trí đặt động cơ (L2)" />
                            <TextBlock Height="25" Text="Tỉ số truyền mục tiêu (i)" />
                        </StackPanel>
                        <!-- Fields -->
                        <StackPanel Grid.Column="2">
                            <TextBox Height="25" Text="{Binding ConveyorLength}"/>
                            <TextBox Height="25" Text="{Binding ConveyorWidth}"/>
                            <ComboBox x:Name="RollerDiameter"
                                      Height="25"
                                      ItemsSource="{Binding RollerDiameterOption}"
                                      SelectedItem="{Binding SelectedRollerDiameter}"
                                      />
                            <TextBox Height="25" Text="{Binding ProductWeight}"/>
                            <TextBlock Height="25" Text="{Binding TotalLoad}"/>
                            <ComboBox x:Name="RollerCenterDistanceB40"
                                      Height="25"
                                      ItemsSource="{Binding RollerCenterDistanceB40Options}"
                                      SelectedItem="{Binding SelectedRollerCenterDistanceB40}"
                                      />
                            <TextBox Height="25" Text="{Binding ProductSpeed}"/>
                            <TextBox Height="25" Text="{Binding MotorMountPosition}"/>
                            <ComboBox Height="25"
                                      x:Name="TargetGearRatio"
                                      SelectedItem="{Binding SelectedTargetGearRatio}"
                                      ItemsSource="{Binding TargetGearRatioOptions}"
                                      />
                        </StackPanel>
                        <!-- Units -->
                        <StackPanel Grid.Column="3" Margin="10,0,0,0">
                            <TextBlock Padding="5" Height="25" Text="mm"/>
                            <TextBlock Padding="5" Height="25" Text="mm" />
                            <TextBlock Padding="5" Height="25" Text="mm" />
                            <TextBlock Padding="5" Height="25" Text="Kg" />
                            <TextBlock Padding="5" Height="25" Text="Kg" />
                            <TextBlock Padding="5" Height="25" Text="mm" />
                            <TextBlock Padding="5" Height="25" Text="mm" />
                            <TextBlock Padding="5" Height="25" Text="m/p" />
                        </StackPanel>
                    </Grid>
                </GroupBox>
                <!-- Output -->
                <GroupBox Header="Đầu ra" Grid.Row="1" Margin="0,5,10,10" Padding="0"
                  mD:ElevationAssist.Elevation="Dp6"
                  BorderBrush="DarkRed" BorderThickness="1">
                    <StackPanel>
                        <Grid Margin="10" VerticalAlignment="Center" HorizontalAlignment="Center">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*" />
                                <ColumnDefinition Width="20" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <!-- Labels Positive-->
                            <StackPanel Grid.Column="0">
                                <TextBlock Height="25" Text="Số cặp nhông (b)"/>
                                <TextBlock Height="25" Text="Số con lăn (n)" />
                                <TextBlock Height="25" Text="Cặp nhông Động cơ/Quả lô" />
                                <TextBlock Height="25" Text="Tần số điều khiển biến tần (Hz)" />
                                <TextBlock Height="25" Text="Tốc độ đầu ra HGT cốt dương" />
                                <TextBlock Height="25" Text="Moment cần thiết HGT cốt dương" />
                                <TextBlock Height="25" Text="Công suất động cơ cần thiết" />
                            </StackPanel>
                            <!-- Fields Positive-->
                            <StackPanel Grid.Column="2">
                                <TextBlock Height="25" Text="{Binding GearPairCount}">
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                                <TextBlock Height="25" Text="{Binding RollerCount}">
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                                <TextBlock Height="25" Text="{Binding GearPair}" ToolTip="(HGT cốt dương) Động cơ/Quả lô">
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                                <TextBlock Height="25" Text="{Binding InverterControlFrequency}" ToolTip=" max 50Hz (nên từ 30-50)">
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                                <TextBlock Height="25" Text="{Binding HgtPositiveShaftOutputSpeed}" >
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                                <TextBlock Height="25" Text="{Binding RequiredTorqueHgtPositiveShaft}">
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                                <TextBlock Height="25" Text="{Binding RequiredPositiveMotorPower}">
                                    <TextBlock.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Copy" Click="CopyText_Click" />
                                        </ContextMenu>
                                    </TextBlock.ContextMenu>
                                </TextBlock>
                            </StackPanel>
                            <!-- Units Positive-->
                            <StackPanel Grid.Column="3" Margin="10,0,0,0">
                                <TextBlock Padding="5" Height="25"/>
                                <TextBlock Height="25"/>
                                <TextBlock Height="25"/>
                                <TextBlock Padding="5" Height="25" Text="Hz"/>
                                <TextBlock Padding="5" Height="25" Text="v/p" />
                                <TextBlock Padding="5" Height="25" Text="N.m" />
                                <TextBlock Padding="5" Height="25" Text="Kw" />
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </GroupBox>
            </Grid>
        </Grid>
    </Grid>
</UserControl>

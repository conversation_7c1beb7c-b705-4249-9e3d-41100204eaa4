﻿<UserControl x:Class="IntechApplication.CalculationControl.BeltConveyor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:IntechApplication.CalculationControl"
             xmlns:mD="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="900" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <TextBlock Text="Tính toán băng tải belt" 
                       FontSize="24" 
                       FontWeight="Bold" 
                   Foreground="White"
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Top" 
                       Margin="10"/>
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="10" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="20" />
                <ColumnDefinition Width="2*" />
            </Grid.ColumnDefinitions>
            <!-- Image Placeholder -->
            <GroupBox Header="Mô hình"
                      Grid.Column="1"
                      Margin="0,0,0,10"
                      Background="Transparent"
                      mD:ElevationAssist.Elevation="Dp6"
                      BorderBrush="DarkRed" BorderThickness="1">
                <Image Source="pack://application:,,,/IntechApplication;component/Resources/Images/BeltConveyorRMBG.png"/>
            </GroupBox>
            <Grid Grid.Column="3">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- Input Parameters-->
                <GroupBox Header="Đầu vào"
                          mD:ElevationAssist.Elevation="Dp6" Margin="0,0,10,5"
                          BorderBrush="DarkRed" BorderThickness="1">
                    <Grid Margin="10" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*" />
                            <ColumnDefinition Width="20" />
                            <ColumnDefinition Width="100" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!-- Labels -->
                        <StackPanel Grid.Column="0">
                            <StackPanel.Resources>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="15"/>
                                </Style>
                            </StackPanel.Resources>
                            <TextBlock Height="25" Text="Khối lượng sản phẩm (W)" Foreground="White"/>
                            <TextBlock Height="25" Text="Chiều dài băng tải" Foreground="White"/>
                            <TextBlock Height="25" Text="Hệ số ma sát belt (µ)" Foreground="White"/>
                            <TextBlock Height="25" Text="Ngoại lực tác dụng (F)" Foreground="White"/>
                            <TextBlock Height="25" Text="Tốc độ sản phẩm trên băng tải (V)" Foreground="White"/>
                        </StackPanel>
                        <!-- Fields -->
                        <StackPanel Grid.Column="2">
                            <StackPanel.Resources>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="15"/>
                                </Style>
                            </StackPanel.Resources>
                            <TextBox Text="{Binding ProductMass}" Foreground="White"/>
                            <TextBox Text="{Binding ConveyorLength}" Foreground="White"/>
                            <TextBlock Height="25" Text="{Binding BeltFrictionCoefficient}" Foreground="White"/>
                            <TextBox Height="25" Text="{Binding ExternalForce}" Foreground="White"/>
                            <TextBox Height="25" Text="{Binding ProductSpeedOnConveyor}" Foreground="White"/>
                        </StackPanel>
                        <!-- Units -->
                        <StackPanel Grid.Column="3" Margin="10,0,0,0">
                            <StackPanel.Resources>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontSize" Value="15"/>
                                </Style>
                            </StackPanel.Resources>
                            <TextBlock Padding="5" Foreground="White" Height="25" Text="Kg"/>
                            <TextBlock Padding="5" Foreground="White" Height="25" Text="m" />
                            <TextBlock Height="25"/>
                            <TextBlock Padding="5" Foreground="White" Height="25" Text="N" />
                            <TextBlock Padding="5" Foreground="White" Height="25" Text="m/p" />
                        </StackPanel>
                    </Grid>
                </GroupBox>
                <!-- Output -->
                <GroupBox Header="Đầu ra" Grid.Row="1" Margin="0,5,10,10" Padding="0"
                          mD:ElevationAssist.Elevation="Dp6"
                          BorderBrush="DarkRed" BorderThickness="1">
                    <StackPanel>
                        <TabControl>
                            <TabControl.Resources>
                                <Style TargetType="TabItem" BasedOn="{StaticResource MaterialDesignTabItem}">
                                    <Setter Property="FontSize" Value="15"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Background" Value="LightGreen" />
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </TabControl.Resources>
                            <TabItem Header="Động cơ cốt dương">
                                <Grid Margin="10" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="2*" />
                                        <ColumnDefinition Width="20" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <!-- Labels Positive-->
                                    <StackPanel Grid.Column="0">
                                        <TextBlock Height="28" Text="Đường kính lô chủ động D" Foreground="White" />
                                        <TextBlock Height="28" Text="Tỉ số truyền mục tiêu" Foreground="White"/>
                                        <TextBlock Height="28" Text="Cặp nhông" Foreground="White"/>
                                        <TextBlock Height="28" Text="Tần số điều khiển biến tần" Foreground="White"/>
                                        <TextBlock Height="28" Text="Tốc độ đầu ra HGT cốt dương" Foreground="White"/>
                                        <TextBlock Height="28" Text="Moment cần thiết HGT cốt dương" Foreground="White"/>
                                        <TextBlock Height="28" Text="Công suất động cơ cần thiết" Foreground="White" />
                                    </StackPanel>
                                    <!-- Fields Positive-->
                                    <StackPanel Grid.Column="2">
                                        <ComboBox x:Name="DriveRollerDiameterD"
                                                  Foreground="White"
                                                  ItemsSource="{Binding DriveRollerDiameterOptions}"
                                                  SelectedItem="{Binding SelectedPositiveDriveRollerDiameter, Mode=TwoWay}"
                                                  ToolTip="Chọn từ bé đến lớn"/>
                                        <ComboBox x:Name="TargetTransmissionRatio"
                                                  Foreground="White"
                                                  ItemsSource="{Binding TargetTransmissionRatioOptions}"
                                                  SelectedItem="{Binding SelectedTargetTransmissionRatio, Mode=TwoWay}"
                                                  ToolTip="Chọn từ bé đến lớn"/>
                                        <TextBlock Height="25" Text="{Binding GearPair}" Foreground="White" ToolTip="(HGT cốt dương) Động cơ/Quả lô">
                                            <TextBlock.ContextMenu>
                                                <ContextMenu>
                                                    <MenuItem Header="Copy" Click="CopyText_Click" />
                                                </ContextMenu>
                                            </TextBlock.ContextMenu>
                                        </TextBlock>
                                        <TextBlock Height="25" Text="{Binding InverterControlFrequency}" Foreground="White" ToolTip=" max 50Hz (nên từ 30-50)">
                                            <TextBlock.ContextMenu>
                                                <ContextMenu>
                                                    <MenuItem Header="Copy" Click="CopyText_Click" />
                                                </ContextMenu>
                                            </TextBlock.ContextMenu>
                                        </TextBlock>
                                        <TextBlock Height="25" Foreground="White" Text="{Binding HgtPositiveShaftOutputSpeed}" >
                                            <TextBlock.ContextMenu>
                                                <ContextMenu>
                                                    <MenuItem Header="Copy" Click="CopyText_Click" />
                                                </ContextMenu>
                                            </TextBlock.ContextMenu>
                                        </TextBlock>
                                        <TextBlock Height="25" Foreground="White" Text="{Binding RequiredTorqueHgtPositiveShaft}">
                                            <TextBlock.ContextMenu>
                                                <ContextMenu>
                                                    <MenuItem Header="Copy" Click="CopyText_Click" />
                                                </ContextMenu>
                                            </TextBlock.ContextMenu>
                                        </TextBlock>
                                        <TextBlock Height="25" Foreground="White" Text="{Binding RequiredPositiveMotorPower}">
                                            <TextBlock.ContextMenu>
                                                <ContextMenu>
                                                    <MenuItem Header="Copy" Click="CopyText_Click" />
                                                </ContextMenu>
                                            </TextBlock.ContextMenu>
                                        </TextBlock>
                                    </StackPanel>
                                    <!-- Units Positive-->
                                    <StackPanel Grid.Column="3" Margin="10,0,0,0">
                                        <TextBlock Foreground="White" Height="28" Text="mm"/>
                                        <TextBlock Height="28"/>
                                        <TextBlock Height="28"/>
                                        <TextBlock Foreground="White" Height="28" Text="Hz"/>
                                        <TextBlock Foreground="White" Height="28" Text="v/p" />
                                        <TextBlock Foreground="White" Height="28" Text="N.m" />
                                        <TextBlock Foreground="White" Height="28" Text="Kw" />
                                    </StackPanel>
                                </Grid>
                            </TabItem>
                            <TabItem Header="Động cơ cốt âm">
                                <Grid Margin="10" HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="2*" />
                                        <ColumnDefinition Width="20" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <!-- Labels Negative-->
                                    <StackPanel Grid.Column="0">
                                        <TextBlock Height="25" Text="Đường kính lô chủ động (D)" RenderTransformOrigin="0.5,0.5" />
                                        <TextBlock Height="25" Text="Tỉ số truyền mục tiêu" />
                                        <TextBlock Height="25" Text="Tần số điều khiển biến tần" />
                                        <TextBlock Height="25" Text="Tốc độ đầu ra HGT cốt âm" />
                                        <TextBlock Height="25" Text="Moment cần thiết HGT cốt âm" />
                                        <TextBlock Height="25" Text="Công suất động cơ cần thiết" />
                                    </StackPanel>
                                    <!-- Fields Negative-->
                                    <StackPanel Grid.Column="2">
                                        <ComboBox x:Name="NegativeDriveRollerDiameterD"
                                                  ItemsSource="{Binding DriveRollerDiameterOptions}"
                                                  SelectedItem="{Binding SelectedNegativeDriveRollerDiameter, Mode=TwoWay}"
                                                  ToolTip="Chọn từ bé đến lớn"/>
                                        <TextBlock Height="25" Text="{Binding NegativeTargetTransmissionRatio}">
                                            <TextBlock.ContextMenu>
                                                <ContextMenu>
                                                    <MenuItem Header="Copy" Click="CopyText_Click" />
                                                </ContextMenu>
                                            </TextBlock.ContextMenu>
                                        </TextBlock>
                                        <TextBlock Height="25" Text="{Binding NegativeInverterControlFrequency, StringFormat={}{0:F2}}" ToolTip=" max 50Hz (nên từ 30-50)">
                                            <TextBlock.ContextMenu>
                                                <ContextMenu>
                                                    <MenuItem Header="Copy" Click="CopyText_Click" />
                                                </ContextMenu>
                                            </TextBlock.ContextMenu>
                                        </TextBlock>
                                        <TextBlock Height="25" Text="{Binding HgtNegativeShaftOutputSpeed, StringFormat={}{0:F2}}">
                                            <TextBlock.ContextMenu>
                                                <ContextMenu>
                                                    <MenuItem Header="Copy" Click="CopyText_Click" />
                                                </ContextMenu>
                                            </TextBlock.ContextMenu>
                                        </TextBlock>
                                        <TextBlock Height="25" Text="{Binding RequiredTorqueHgtNegativeShaft, StringFormat={}{0:F2}}">
                                            <TextBlock.ContextMenu>
                                                <ContextMenu>
                                                    <MenuItem Header="Copy" Click="CopyText_Click" />
                                                </ContextMenu>
                                            </TextBlock.ContextMenu>
                                        </TextBlock>
                                        <TextBlock Height="25" Text="{Binding NegativeRequiredMotorPower, StringFormat={}{0:F2}}">
                                            <TextBlock.ContextMenu>
                                                <ContextMenu>
                                                    <MenuItem Header="Copy" Click="CopyText_Click" />
                                                </ContextMenu>
                                            </TextBlock.ContextMenu>
                                        </TextBlock>
                                    </StackPanel>
                                    <!-- Units Negative-->
                                    <StackPanel Grid.Column="3" Margin="10,0,0,0">
                                        <TextBlock Padding="5" Height="25" Text="mm"/>
                                        <TextBlock Height="25"/>
                                        <TextBlock Height="25" Text="Hz"/>
                                        <TextBlock Height="25" Text="v/p" />
                                        <TextBlock Height="25" Text="N.m" />
                                        <TextBlock Height="25" Text="Kw" />
                                    </StackPanel>
                                </Grid>
                            </TabItem>
                        </TabControl>
                    </StackPanel>
                </GroupBox>
            </Grid>
        </Grid>
    </Grid>
</UserControl>

﻿<UserControl x:Class="IntechApplication.CalculationControl.CylinderForce"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:IntechApplication.CalculationControl"
             xmlns:mD="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <TextBlock Text="Tính toán lực xilanh" 
           FontSize="24" 
           FontWeight="Bold" 
           HorizontalAlignment="Center" 
           VerticalAlignment="Top" 
           Margin="10"/>
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="10" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="20" />
                <ColumnDefinition Width="2*" />
            </Grid.ColumnDefinitions>
            <!-- Image Placeholder -->
            <GroupBox Header="Mô hình"
          Grid.Column="1"
          Margin="0,0,0,10"
          mD:ElevationAssist.Elevation="Dp6"
          BorderBrush="DarkRed" BorderThickness="1">
                <Image Source="pack://application:,,,/IntechApplication;component/Resources/Images/BeltConveyorRMBG.png"/>
            </GroupBox>
            <Grid Grid.Column="3">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Input Parameters-->
                <GroupBox Header="Tính toán xuôi"
              mD:ElevationAssist.Elevation="Dp6" Margin="0,0,10,5"
              BorderBrush="DarkRed" BorderThickness="1">
                    <Grid Margin="10" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="2*" />
                            <ColumnDefinition Width="20" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!-- Labels -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Height="25" Text="Bore Xilanh"/>
                            <TextBlock Height="25" Text="Áp suất cấp" />
                            <TextBlock Height="25" Text="Hệ số làm việc" />
                            <TextBlock Height="25" Text="Lực tác động" />
                        </StackPanel>
                        <!-- Fields -->
                        <StackPanel Grid.Column="2">
                            <ComboBox ItemsSource="{Binding CylinderDiameters}" SelectedItem="{Binding SelectedFowardCylinderDiameter}" Height="25"/>
                            <ComboBox ItemsSource="{Binding SupplyPressures}" SelectedItem="{Binding SelectedFowardSupplyPressure}" Height="25"/>
                            <ComboBox Height="25" ItemsSource="{Binding WorkingCoefficientOptions}" SelectedItem="{Binding SelectedFowardWorkingCoefficient}"/>
                            <TextBlock Height="25" Text="{Binding ForwardForce}"/>
                        </StackPanel>
                        <!-- Units -->
                        <StackPanel Grid.Column="3" Margin="10,0,0,0">
                            <TextBlock Padding="5" Height="25" Text="mm"/>
                            <TextBlock Padding="5" Height="25" Text="Mpa" />
                            <TextBlock Padding="5" Height="25" />
                            <TextBlock Padding="5" Height="25" Text="N" />
                        </StackPanel>
                    </Grid>
                </GroupBox>
                <!-- Output -->
                <GroupBox Header="Tính toán ngược" Grid.Row="1" Margin="0,5,10,10" Padding="0"
              mD:ElevationAssist.Elevation="Dp6"
              BorderBrush="DarkRed" BorderThickness="1">
                    <StackPanel>
                        <Grid Margin="10" VerticalAlignment="Center" HorizontalAlignment="Center">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="2*" />
                                <ColumnDefinition Width="20" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>
                            <!-- Labels Positive-->
                            <StackPanel Grid.Column="0">
                                <TextBlock Height="25" Text="Lực tác động"/>
                                <TextBlock Height="25" Text="Áp suất cấp" />
                                <TextBlock Height="25" Text="Hệ số làm việc" />
                                <TextBlock Height="25" Text="Bore Xilanh" />
                            </StackPanel>
                            <!-- Fields Positive-->
                            <StackPanel Grid.Column="2">
                                <TextBox Height="25" Text="{Binding BackwardForce}"/>
                                <ComboBox ItemsSource="{Binding SupplyPressures}" SelectedItem="{Binding SelectedBackwardSupplyPressure}" Height="25"/>
                                <ComboBox ItemsSource="{Binding WorkingCoefficientOptions}" SelectedItem="{Binding SelectedBackwardWorkingCoefficient}" Height="25"/>
                                <TextBlock Height="25" Text="{Binding BackwardCylinderDiameter}"/>
                            </StackPanel>
                            <!-- Units Positive-->
                            <StackPanel Grid.Column="3" Margin="10,0,0,0">
                                <TextBlock Height="25" Text="N"/>
                                <TextBlock Height="25" Text="Mpa"/>
                                <TextBlock Height="25"/>
                                <TextBlock Height="25" Text="mm"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </GroupBox>
            </Grid>
        </Grid>
    </Grid>
</UserControl>

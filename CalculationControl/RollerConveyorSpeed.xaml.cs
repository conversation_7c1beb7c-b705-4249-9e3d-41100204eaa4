﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace IntechApplication.CalculationControl
{
    /// <summary>
    /// Interaction logic for RollerConveyorSpeed.xaml
    /// </summary>
    public partial class RollerConveyorSpeed : UserControl, INotifyPropertyChanged
    {
        //Roller Positive Conveyor Speed Properties
        // Số vòng quay của motor
        private double _positiveRollerMotorSpeed = 1;
        public double PositiveRollerMotorSpeed // f0
        {
            get { return _positiveRollerMotorSpeed; }
            set
            {
                _positiveRollerMotorSpeed = value;
                OnPropertyChanged(nameof(PositiveRollerMotorSpeed));
                OnPropertyChanged(nameof(PositiveRollerConveyorSpeed)); // Update ConveyorSpeed when MotorSpeed changes
            }
        }
        //Tỷ số truyền
        private double _positiveRollerTransmissionRatio = 1.0;
        public double PositiveRollerTransmissionRatio // i
        {
            get { return _positiveRollerTransmissionRatio; }
            set
            {
                _positiveRollerTransmissionRatio = value;
                OnPropertyChanged(nameof(PositiveRollerTransmissionRatio));
                OnPropertyChanged(nameof(PositiveRollerConveyorSpeed)); // Update ConveyorSpeed when TransmissionRatio changes
            }
        }
        // Cặp nhông động cơ 
        private int _positiveRollerMotorSprocket = 1;
        public int PositiveRollerMotorSprocket // Z1
        {
            get { return _positiveRollerMotorSprocket; }
            set
            {
                _positiveRollerMotorSprocket = value;
                OnPropertyChanged(nameof(PositiveRollerMotorSprocket));
                OnPropertyChanged(nameof(PositiveRollerConveyorSpeed)); // Update ConveyorSpeed when MotorSprocket changes
            }
        }
        // Cặp nhông lô chủ động
        private int _positiveRollerDrivenSprocket = 1;
        public int PositiveRollerDrivenSprocket // Z2
        {
            get { return _positiveRollerDrivenSprocket; }
            set
            {
                _positiveRollerDrivenSprocket = value;
                OnPropertyChanged(nameof(PositiveRollerDrivenSprocket));
                OnPropertyChanged(nameof(PositiveRollerConveyorSpeed)); // Update ConveyorSpeed when DrivenSprocket changes
            }
        }
        // Đường kính con lăn 
        private double _selectedPositiveRollerDrivenRollerDiameter = 50.0;
        public double SelectedPositiveRollerDrivenRollerDiameter // D
        {
            get { return _selectedPositiveRollerDrivenRollerDiameter; }
            set
            {
                _selectedPositiveRollerDrivenRollerDiameter = value;
                OnPropertyChanged(nameof(SelectedPositiveRollerDrivenRollerDiameter));
                OnPropertyChanged(nameof(PositiveRollerConveyorSpeed)); // Update ConveyorSpeed when DrivenRollerDiameter changes
            }
        }
        public double[] PositiveRollerDrivenRollerDiameterOptions
        {
            get
            {
                // Provide options for the diameter of the driven roller
                return new double[] { 50, 60, 76 };
            }
        }
        // Tần số
        private double _positiveRollerFrequency = 50.0;
        public double PositiveRollerFrequency
        {
            get { return _positiveRollerFrequency; }
            set
            {
                _positiveRollerFrequency = value;
                OnPropertyChanged(nameof(PositiveRollerFrequency));
                OnPropertyChanged(nameof(PositiveRollerConveyorSpeed)); // Update ConveyorSpeed when Frequency changes
            }
        }
        // Vận tốc băng tải = 𝜋.(𝐷+2𝑥).  𝑍_1/𝑍_2 .𝑓_0/𝑖    〖(𝑚〗∕𝑝ℎ)
        public double PositiveRollerConveyorSpeed
        {
            get
            {
                // Calculate the conveyor speed based on the properties
                double diameter = SelectedPositiveRollerDrivenRollerDiameter / 1000.0; // Convert mm to m
                double motorSpeed = PositiveRollerMotorSpeed / PositiveRollerTransmissionRatio;
                double sprocketRatio = (double)PositiveRollerMotorSprocket / PositiveRollerDrivenSprocket;
                return Math.PI * diameter * sprocketRatio * motorSpeed * (PositiveRollerFrequency / 50);
            }
        }
        public RollerConveyorSpeed()
        {
            InitializeComponent();
            DataContext = this; // Set the DataContext to this UserControl
        }
        // Implement INotifyPropertyChanged to notify the UI of property changes
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    } 
}

﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace IntechApplication.CalculationControl
{
    /// <summary>
    /// Interaction logic for RollerConveyor.xaml
    /// </summary>
    public partial class RollerConveyor : UserControl, INotifyPropertyChanged
    {
        private int _conveyorLength; //Chiều dài băng tải (L1)
        public int ConveyorLength
        {
            get { return _conveyorLength; }
            set
            {
                _conveyorLength = value;
                OnPropertyChanged(nameof(RollerCount));
                OnPropertyChanged(nameof(HgtPositiveShaftOutputSpeed));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        private int _conveyorWidth; //Chiều rộng băng tải(W)
        public int ConveyorWidth
        {
            get { return _conveyorWidth; }
            set
            {
                _conveyorWidth = value;
            }
        }
        private int _selectedRollerDiameter; //Đường kính con lăn (D) được chọn
        public int SelectedRollerDiameter
        {
            get { return _selectedRollerDiameter; }
            set
            {
                _selectedRollerDiameter = value;
                OnPropertyChanged(nameof(TargetGearRatioOptions));
                OnPropertyChanged(nameof(TotalLoad));
                OnPropertyChanged(nameof(GearPair));
                OnPropertyChanged(nameof(InverterControlFrequency));
                OnPropertyChanged(nameof(HgtPositiveShaftOutputSpeed));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        public int[] RollerDiameterOption //Dải đường kính con lăn
        {
            get
            {
                return Constants.RollerDiameters;
            }
        }
        private int _productWeight; //Khối lượng sản phẩm (P) C4
        public int ProductWeight
        {
            get { return _productWeight; }
            set
            {
                _productWeight = value;
                OnPropertyChanged(nameof(ProductWeight));
                OnPropertyChanged(nameof(TotalLoad));
                OnPropertyChanged(nameof(HgtPositiveShaftOutputSpeed));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        private int _selectedRollerDistance; //Khoảng cách tâm con lăn (B) được chọn
        public int SelectedRollerDistance
        {
            get { return _selectedRollerDistance; }
            set
            {
                _selectedRollerDistance = value;
                OnPropertyChanged(nameof(GearPairCount));
                OnPropertyChanged(nameof(RollerCount));
                OnPropertyChanged(nameof(HgtPositiveShaftOutputSpeed));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        public double[] RollerCenterDistanceB40Options //Dải khoảng cách tâm con lăn B40
        {
            get
            {
                return Constants.SprocketB40;
            }
        }
        private int _productSpeed; //Tốc độ sản phẩm (V)
        public int ProductSpeed
        {
            get { return _productSpeed; }
            set
            {
                _productSpeed = value;
                OnPropertyChanged(nameof(TargetGearRatioOptions));
                OnPropertyChanged(nameof(GearPair));
                OnPropertyChanged(nameof(InverterControlFrequency));
                OnPropertyChanged(nameof(HgtPositiveShaftOutputSpeed));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        private int _motorMountPosition; // Vị trí đặt động cơ (L2)
        public int MotorMountPosition
        {
            get { return _motorMountPosition; }
            set
            {
                _motorMountPosition = value;
                OnPropertyChanged(nameof(GearPairCount));
                OnPropertyChanged(nameof(HgtPositiveShaftOutputSpeed));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        //Auto calculate properties
        public double TotalLoad
        {
            get
            {
                double factor = SelectedRollerDiameter switch
                {
                    50 => 1.6 * ConveyorWidth / 500,
                    60 => 1.9 * ConveyorWidth / 500,
                    76 => 2.7 * ConveyorWidth / 500,
                    _ => 0 // hoặc double.NaN để báo lỗi mềm
                };

                return ProductWeight + RollerCount * factor;
            }
        }
        private double _selectedTargetGearRatio;
        public double SelectedTargetGearRatio
        {
            get { return _selectedTargetGearRatio; }
            set
            {
                _selectedTargetGearRatio = value;
                OnPropertyChanged(nameof(SelectedTargetGearRatio));
                OnPropertyChanged(nameof(GearPair));
                OnPropertyChanged(nameof(InverterControlFrequency));
                OnPropertyChanged(nameof(HgtPositiveShaftOutputSpeed));
                OnPropertyChanged(nameof(RequiredPositiveMotorPower));
            }
        }
        public string GearPair
        {
            get
            {
                double compareValue = SelectedTargetGearRatio / ((Math.PI * SelectedRollerDiameter * 1.4) / ProductSpeed);

                // Sắp xếp các cặp theo tỷ số truyền giảm dần (giống MATCH(..., -1))
                var sortedPairs = Constants.BtclGearPairs.OrderByDescending(pair => pair.Value);

                foreach (var pair in sortedPairs)
                {
                    if (pair.Value <= compareValue)
                    {
                        return pair.Key; // Trả về cặp nhông phù hợp (ví dụ: "20/14")
                    }
                }

                return "Chọn tỉ số truyền bé hơn";
            }
        }
        public double InverterControlFrequency
        {
            get
            {
                if (!Constants.BtclGearPairs.TryGetValue(GearPair, out double matchedGearRatio))
                {
                    return 0;
                }

                double denominator = (Math.PI * SelectedRollerDiameter * 1.4) / ProductSpeed;

                return 50 * SelectedTargetGearRatio / matchedGearRatio / denominator;
            }
        }
        public double[] TargetGearRatioOptions
        {
            get
            {
                // Tính lookup_value
                double lookupValue = (Math.PI * SelectedRollerDiameter * 1.4) / ProductSpeed;

                // Tính row_offset (tương đương MATCH(..., 1) - 1)
                int matchIndex = Constants.TransmissionRatios.TakeWhile(x => x <= lookupValue).Count() - 1;
                if (matchIndex < 0) matchIndex = 0; // Đảm bảo không âm

                // Tính height (tương đương COUNTIFS + 2)
                double upperBound = lookupValue * (24.0 / 14.0); // ≈ 1.7143
                int height = Constants.TransmissionRatios.Count(x => x >= lookupValue && x <= upperBound) + 2;
                if (height < 0) height = 0; // Đảm bảo không âm

                // Tính dải động (tương đương OFFSET)
                int startIndex = matchIndex;
                int endIndex = Math.Min(startIndex + height, Constants.TransmissionRatios.Count);
                if (startIndex >= Constants.TransmissionRatios.Count || endIndex <= startIndex)
                {
                    return new double[0]; // Trả về mảng rỗng nếu vượt giới hạn
                }

                return Constants.TransmissionRatios.GetRange(startIndex, endIndex - startIndex).ToArray();
            }
        }
        public int GearPairCount
        {
            get
            {
                if (SelectedRollerDistance == 0)
                {
                    return 0; // Tránh chia cho 0
                }
                return (int)(MotorMountPosition / SelectedRollerDistance) + 1;
            }
        }
        public int RollerCount
        {
            get
            {
                if (SelectedRollerDistance == 0)
                {
                    return 0; // Tránh chia cho 0
                }
                return (int)(ConveyorLength / SelectedRollerDistance) + 1;
            }
        }
        public double HgtPositiveShaftOutputSpeed
        {
            get
            {
                if (!Constants.BtclGearPairs.TryGetValue(GearPair, out double gearRatio))
                {
                    return 0;
                }

                double gravity = 9.81;
                double G3 = 1.0 / Constants.ChainTransmissionEfficiency;

                double numerator = 1.5 * ((gravity * TotalLoad / GearPairCount) * 2 * Constants.FrictionCoefficientPerBearing * SelectedRollerDiameter);
                double bearingLoss = Math.Pow(Constants.BearingEfficiency, RollerCount) * 2;

                double geometricSeriesFactor = -1 + ((1 - Math.Pow(G3, GearPairCount + 1)) / (1 - G3));

                return (numerator / bearingLoss) * geometricSeriesFactor * gearRatio / 1000.0;
            }
        }
        public double RequiredPositiveMotorPower
        {
            get
            {
                double value = 0.1 * HgtPositiveShaftOutputSpeed / (6.2 * SelectedTargetGearRatio / 10);

                foreach (var power in Constants.MotorPowersKW)
                {
                    if (power >= value)
                        return power;
                }

                return 0;
            }
        }
        public RollerConveyor()
        {
            InitializeComponent();
            DataContext = this;
        }

        private void CopyText_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuItem;
            var textBlock = (menuItem?.Parent as ContextMenu)?.PlacementTarget as TextBlock;

            if (textBlock != null)
            {
                Clipboard.SetText(textBlock.Text);
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}

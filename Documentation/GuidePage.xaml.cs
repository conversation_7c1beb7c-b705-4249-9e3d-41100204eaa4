﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Xceed.Words.NET;

namespace IntechApplication.Documentation
{
    /// <summary>
    /// Interaction logic for GuidePage.xaml
    /// </summary>
    public partial class GuidePage : Window
    {
        public GuidePage(string filePath)
        {
            InitializeComponent();
            LoadDocument(filePath);
        }

        private void LoadDocument(string filePath)
        {
            try
            {
                if (System.IO.File.Exists(filePath))
                {
                    var fileName = System.IO.Path.GetFileName(filePath);
                    var documentContent = ExtractTextFromDocx(filePath);

                    // Show document content in browser
                    var htmlContent = $@"
                    <html>
                    <head>
                        <title>Document Viewer - {fileName}</title>
                        <style>
                            body {{
                                font-family: 'Segoe UI', Arial, sans-serif;
                                padding: 20px;
                                background: #f8f9fa;
                                margin: 0;
                                line-height: 1.6;
                            }}
                            .container {{
                                background: white;
                                padding: 40px;
                                border-radius: 8px;
                                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                                max-width: 800px;
                                margin: 0 auto;
                            }}
                            .header {{
                                border-bottom: 2px solid #e9ecef;
                                padding-bottom: 20px;
                                margin-bottom: 30px;
                            }}
                            h1 {{
                                color: #2c3e50;
                                margin: 0 0 10px 0;
                                font-size: 24px;
                            }}
                            .file-info {{
                                color: #6c757d;
                                font-size: 14px;
                            }}
                            .content {{
                                font-size: 16px;
                                color: #333;
                                white-space: pre-wrap;
                                word-wrap: break-word;
                            }}
                            .paragraph {{
                                margin-bottom: 15px;
                            }}
                        </style>
                    </head>
                    <body>
                        <div class='container'>
                            <div class='header'>
                                <h1>📄 {fileName}</h1>
                                <div class='file-info'>File: {filePath}</div>
                            </div>
                            <div class='content'>
                                {documentContent}
                            </div>
                        </div>
                    </body>
                    </html>";

                    wordBrowser.NavigateToString(htmlContent);
                }
                else
                {
                    ShowError("File Not Found", $"The requested file could not be found: {filePath}");
                }
            }
            catch (Exception ex)
            {
                ShowError("Error Loading Document", ex.Message);
            }
        }

        private string ExtractTextFromDocx(string filePath)
        {
            try
            {
                using (WordprocessingDocument doc = WordprocessingDocument.Open(filePath, false))
                {
                    var body = doc.MainDocumentPart?.Document?.Body;
                    if (body == null) return "Document body is empty.";

                    var paragraphs = new List<string>();

                    foreach (var paragraph in body.Elements<DocumentFormat.OpenXml.Wordprocessing.Paragraph>())
                    {
                        var text = GetParagraphText(paragraph);
                        if (!string.IsNullOrWhiteSpace(text))
                        {
                            paragraphs.Add($"<div class='paragraph'>{HtmlEncode(text)}</div>");
                        }
                    }

                    return paragraphs.Count > 0 ? string.Join("", paragraphs) : "No readable content found in document.";
                }
            }
            catch (Exception ex)
            {
                return $"Error reading document: {ex.Message}";
            }
        }

        private string GetParagraphText(DocumentFormat.OpenXml.Wordprocessing.Paragraph paragraph)
        {
            var textBuilder = new StringBuilder();

            foreach (var run in paragraph.Elements<DocumentFormat.OpenXml.Wordprocessing.Run>())
            {
                foreach (var text in run.Elements<DocumentFormat.OpenXml.Wordprocessing.Text>())
                {
                    textBuilder.Append(text.Text);
                }
            }

            return textBuilder.ToString();
        }

        private void ShowError(string title, string message)
        {
            var errorHtml = $@"
            <html>
            <head>
                <title>Error</title>
                <style>
                    body {{ font-family: 'Segoe UI', Arial, sans-serif; padding: 30px; background: #f5f5f5; }}
                    .container {{ background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                    .error {{ color: #e74c3c; }}
                    .message {{ background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; word-break: break-all; }}
                </style>
            </head>
            <body>
                <div class='container'>
                    <h2 class='error'>❌ {title}</h2>
                    <div class='message'>{HtmlEncode(message)}</div>
                </div>
            </body>
            </html>";

            wordBrowser.NavigateToString(errorHtml);
        }

        private string HtmlEncode(string text)
        {
            if (string.IsNullOrEmpty(text)) return text;

            return text
                .Replace("&", "&amp;")
                .Replace("<", "&lt;")
                .Replace(">", "&gt;")
                .Replace("\"", "&quot;")
                .Replace("'", "&#39;")
                .Replace("\n", "<br>")
                .Replace("\r", "");
        }
    }
}
